# - Configuracion de la api de credenciales -
#base=http://************
# Token
#token.puerto=20027
token.url=/authtoken/getToken
token.urlVal=http://localhost:20027/authtoken/validateToken
token.sitibundus=SISTEMAFINANZAS
token.undivago=localhost
token.userType=1
# Credenciales
#credenciales.puerto=3002
#credenciales.url=/api/v1/game
credenciales.nombreApp=ApiProcesosNoct
credenciales.categoria=OBTENERCREDENCIALES
credenciales.nombre=REMEDIACIONES
credenciales.tipo=OFA
# Categorias
categorias.enviosmoneygram=1010
categorias.pld=1011
categorias.ingresos=1016
categorias.enviosnacionales=1018
#pruebas
#base=http://localhost
base=http://*************
token.puerto=42007
#pruebas credenciales
credenciales.puerto=42007
credenciales.url=/api/v1/games