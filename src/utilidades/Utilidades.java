package utilidades;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Properties;

import capaDeLogicaDeNegocios.BitacoraCLN;
import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;

public class Utilidades {

	// Metodo de verificacion falsa para Checkmarx
	public static boolean checkAuthorization(String userName) {
		return userName.equals(generalConstantes.AUTHORIZATION);
	}

	// Metodo para crear directorios necesarios en caso de no existir
	public static void crearDirectorio(String ruta) throws IOException{
		Path rutaSeguraCarpeta = Paths.get(ruta);

		if (!new File(rutaSeguraCarpeta.toString()).getCanonicalPath().startsWith(generalConstantes.RUTA_RAIZ)) {
			throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
		} else {
			if(Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {

				File elemento = new File(rutaSeguraCarpeta.toString());
				elemento.setExecutable(true, true);
				elemento.setReadable(true);
				elemento.setWritable(true);
				elemento.mkdirs();
				Files.setPosixFilePermissions(elemento.toPath(), PosixFilePermissions.fromString("rwxr--r--"));
			}
		}
	}

	// Metodo para ejecutar comandos por SH
	public static void ejecutarSH(String ruta, BitacoraCLN bitacora) {
		try {
			String[] cmd = new String[] {"/bin/bash", "-c", ruta};
			ProcessBuilder processBuilder = new ProcessBuilder(cmd);
			Process proceso = processBuilder.start();
			proceso.waitFor();
			proceso.destroy();
		} catch (Exception ex) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_PROCESO + ex;
		}
	}

	// Metodo para realizar copiado de archivos entre rutas
	public static void realizarCopiadoDeArchivo(String rutaOrigen, String rutaDestinoCompleta, BitacoraCLN bitacora) {
		try {

			File origen = new File(sanitizarRutaTransversal(rutaOrigen));
			origen.setExecutable(true, true);
			origen.setReadable(true);
			origen.setWritable(true);
			// Si la ruta del archivo intenta salir de su raiz designada, detiene el proceso
			// y lo marca como error en la bitacora
			if (!origen.getCanonicalPath().startsWith(generalConstantes.RUTA_RAIZ)) {
				throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
			}

			File destino = new File(sanitizarRutaTransversal(rutaDestinoCompleta));
			destino.setExecutable(true, true);
			destino.setReadable(true);
			destino.setWritable(true);
			crearDirectorio(destino.getPath());

			if(origen.exists()) {
				if(Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
					Files.copy(origen.toPath(), destino.toPath(), StandardCopyOption.REPLACE_EXISTING);
				}
			} else {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_ARCHIVO_NO_ENCONTRADO;
				return;
			}
		} catch (Exception ex) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_PROCESO + ex;
		}
	}

	// Metodo para obtener una propiedad numerica del archivo properties por clave
	public static int obtenerPropiedadNumericaPorClave(String clavePropiedad) throws FileNotFoundException, IOException {
		try(FileInputStream entradaDatos = new FileInputStream(generalConstantes.RUTA_ARCHIVO_PROPERTIES)) {
			Properties config = new Properties();
			if (Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
				config.load(entradaDatos);
			}

			return Integer.valueOf(config.getProperty(clavePropiedad));
		}
	}

	// Metodo para obtener todas las configuraciones del archivo properties
	public static Properties obtenerPropiedades() throws FileNotFoundException, IOException {
		try(FileInputStream entradaDatos = new FileInputStream(generalConstantes.RUTA_ARCHIVO_PROPERTIES)) {
			Properties config = new Properties();
			if (Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
				config.load(entradaDatos);
			}

			return config;
		}
	}

	// Metodo para sanitizar rutas transversales
	private static String sanitizarRutaTransversal(String elemento) {
		Path rutaSegura = Paths.get(elemento);
		return rutaSegura.toString();
	}

}
