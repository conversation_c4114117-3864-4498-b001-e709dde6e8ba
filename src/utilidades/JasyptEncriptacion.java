package utilidades;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.iv.RandomIvGenerator;

import constantes.generalConstantes;

public class JasyptEncriptacion {

	StandardPBEStringEncryptor encriptador;

	public JasyptEncriptacion() {

		// Se inicializa encriptacion
        this.encriptador = new StandardPBEStringEncryptor();
        // Se define la palabra clave de encriptacion
        encriptador.setPassword(generalConstantes.PALABRA_METODO_ENCRIPTACION);
        // Se asigna el metodo de encriptacion
        encriptador.setAlgorithm(generalConstantes.METODO_ENCRIPTACION);
        //Se genera una iteracion a la encriptacion un numero aleatorio de veces
        encriptador.setIvGenerator(new RandomIvGenerator());
	}

	public String cifrar(String cadena) {
		// Cifra una cadena
        String textoEncriptado = encriptador.encrypt(cadena);
        return textoEncriptado;
	}

	public String decifrar(String cadena) {
		// Descifra la cadena
        String textoDesencriptado = encriptador.decrypt(cadena);
		return textoDesencriptado;
	}
}
