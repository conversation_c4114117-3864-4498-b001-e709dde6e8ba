package capaDeLogicaDeNegocios;
/*
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Properties;

import jcifs.context.BaseContext;
import jcifs.smb.NtlmPasswordAuthenticator;
import jcifs.smb.SmbFile;
import jcifs.CIFSContext;
import jcifs.config.PropertyConfiguration;
import jcifs.Configuration;
*/
import utilidades.JasyptEncriptacion;
/*
import utilidades.Utilidades;

import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
*/
public class SambaCLN {
	@SuppressWarnings("unused")
	private static JasyptEncriptacion encriptacion = new JasyptEncriptacion();

	// Sobrecarga del método 'bajarArchivoServidor' para poder recibir el número
	// identificador del proceso que se está ejecutando.
	public static String bajarArchivoServidor(String ip, String path, String usuarioSamba, String cntraSamba, String nombreArchivo,String extension,int idProceso, BitacoraCLN bitacora) throws Exception {
		//return bajarArchivoServidor(ip, path, usuarioSamba, cntraSamba, nombreArchivo, extension, String.valueOf(idProceso), bitacora);
		return "rutaexample";
	}

	// Método que realiza una conexion samba al servidor que se proporcione en parametros
	// para poder descargar el archivo especificado en los parametros
	/*
	public static String bajarArchivoServidor(String ip, String path, String usuarioSamba, String cntraSamba, String nombreArchivo,String extension,String carpeta, BitacoraCLN bitacora) throws Exception {
		String rutaAbsolutaArchivoDescargado = "";
		BufferedWriter bufferEscritura = null;
		BufferedReader bufferLectura = null;
		SmbFile archivoSamba = null;
		LogCLN log = new LogCLN();
		try {
			// Rutas
			String nombreCompletoArchivo = nombreArchivo + extension;
			Path rutaSegura = Paths.get(System.getProperty("user.dir"), "archivos", carpeta);
			Path rutaArchivoDestino = Paths.get(rutaSegura.toString(), nombreCompletoArchivo);

			//crea carpeta
			Utilidades.crearDirectorio(rutaSegura.toString());

			rutaAbsolutaArchivoDescargado = rutaArchivoDestino.toString();
			String rutaSamba = "smb://" + ip + path + nombreCompletoArchivo;

			// Configuracion SAMBA
			BaseContext contexto = null;
			Properties propiedadesSamba  = new Properties();
			propiedadesSamba.setProperty("jcifs.smb.client.enableSMB2", "true");
			propiedadesSamba.setProperty("jcifs.smb.client.dfs.disabled","true");
			Configuration configuracionSamba = new PropertyConfiguration(propiedadesSamba);
			contexto = new BaseContext(configuracionSamba);
			CIFSContext autorizacionSamba = contexto.withCredentials(new NtlmPasswordAuthenticator(
					ip,
					encriptacion.decifrar(usuarioSamba),
					encriptacion.decifrar(cntraSamba)
			));
			archivoSamba = new SmbFile(rutaSamba, autorizacionSamba);

			// Descarga archivo SAMBA
			try(InputStream archivoSambaStream = archivoSamba.getInputStream()) {
				if(Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
					Files.copy(archivoSambaStream, rutaArchivoDestino,StandardCopyOption.REPLACE_EXISTING);
    			}
			} catch (Exception err) {
				throw new Exception(generalConstantes.MENSAJE_ERROR_ARCHIVO_NO_ENCONTRADO);
			}

			Files.setPosixFilePermissions(rutaArchivoDestino, PosixFilePermissions.fromString("rwxr--r--"));
		} catch (IOException e) {
			rutaAbsolutaArchivoDescargado = "";
			if (bitacora != null) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = e.toString();
			} else {
				log.grabarLog(e.toString());
				throw e;
			}
		} finally {
			if (bufferEscritura != null) bufferEscritura.close();
			if (bufferLectura != null) bufferLectura.close();
			if (archivoSamba != null) archivoSamba.close();
		}

		return rutaAbsolutaArchivoDescargado;
	}
	*/
}
