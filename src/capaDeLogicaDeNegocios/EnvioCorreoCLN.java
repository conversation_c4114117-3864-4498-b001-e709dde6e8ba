package capaDeLogicaDeNegocios;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse.BodyHandlers;
import java.nio.charset.StandardCharsets;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

import capaDeDatos.ConsultasCD;
import constantes.configuracionConstantes;
import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import entidades.ConexionInterfase;
import jcifs.util.Base64;
import utilidades.JasyptEncriptacion;

public class EnvioCorreoCLN {

	static ConsultasCD consultasGenerales = new ConsultasCD();

	// Metodo con la logica de envio de correos de alerta de ejecucion y archivo adjunto
		@SuppressWarnings("deprecation")
		public static void enviarCorreosConciliacion(List<String> rutasArchivos, int idConciliacion, int idPaso, BitacoraCLN bitacora) throws IOException, SQLException {
			try {

				boolean esCorrecto = false;
				boolean flagSinDestinatariosAlerta = false;
				boolean flagSinDestinatariosAdjunto = false;
				boolean flagContinuarEnvio = true;

				//Properties propiedades = new Properties();
				@SuppressWarnings("unused")
				String protocolo = "";
				@SuppressWarnings("unused")
				String apodoRemitente = "";
				@SuppressWarnings("unused")
				String remitente = "";
				@SuppressWarnings("unused")
				String claveRemitente = "";
				String cuerpoCorreo = "";
				@SuppressWarnings("unused")
				String host = "";
				@SuppressWarnings("unused")
				String puerto = "";
				@SuppressWarnings("unused")
				String tlsEnable = "";
				@SuppressWarnings("unused")
				String auth = "";


				String sujetoCorreo="";
				String servidorCorreo="";
				String puertoCorreo="";
				String pathApiTokenCorreo="";
				String pathApiCorreo="";
				String usuarioApiCorreo="";
				String contrasenaApiCorreo="";

				ResultSet configuraciones = ConsultasCD.consultarConfiguracionesCorreo();

				while(configuraciones.next()) {
					// Se obtienen las configuraciones para el envío de correos
					String claveConfiguracion = configuraciones.getString("des_clave");

					switch(claveConfiguracion) {
					case configuracionConstantes.CORREO_REMITENTE:
						remitente = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.CORREO_REMITENTE_CLAVE:
						JasyptEncriptacion encriptador = new JasyptEncriptacion();
						claveRemitente = encriptador.decifrar(configuraciones.getString("des_valor_texto"));
						break;

					case configuracionConstantes.CORREO_REMITENTE_APODO:
						apodoRemitente = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.CORREO_PROTOCOLO:
						protocolo = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.CORREO_HOST:
						host = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.CORREO_PUERTO:
						puerto = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.CORREO_STARTTLS_ENABLE:
						tlsEnable = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.CORREO_AUTH:
						auth = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.SERVIDOR_CORREO:
						servidorCorreo = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.PUERTO_CORREO:
						puertoCorreo = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.PATH_API_TOKEN_CORREO:
						pathApiTokenCorreo = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.PATH_API_CORREO:
						pathApiCorreo = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.USUARIO_API_CORREO:
						usuarioApiCorreo = configuraciones.getString("des_valor_texto");
						break;

					case configuracionConstantes.CONTRASENA_API_CORREO:
						contrasenaApiCorreo = configuraciones.getString("des_valor_texto");
						break;

					default:
						break;
					}
				}

				// Se obtienen los correos a enviar para el proceso actual
				ResultSet correosEnviar = ConsultasCD.consultarCorreosEnviar(idConciliacion, idPaso, bitacora.interfaz.iduBitacora);
				// Si existen correos, se envian, si no, se registra error en la bitacora
				if (correosEnviar.isBeforeFirst()) {
					// Se itera cada correo a enviar
					while (correosEnviar.next()) {
						String correoDestinatario ="";
						flagContinuarEnvio = true;

						// Se obtienen los destinatarios del correo a enviar
						ResultSet destinatarios = ConsultasCD.consultarCorreosDestinatarios(correosEnviar.getInt("idu_correo"));
						// Se asignan el destinatario y apodo del remitente desde las configuraciones consultadas
						// Se obtiene el total de destinatarios para agregarlos a un arreglo.
						@SuppressWarnings("unused")
						int totalDestinatarios = 0;
						if(destinatarios != null && destinatarios.last()) {
							totalDestinatarios = destinatarios.getRow();
							destinatarios.beforeFirst();
						} else {
							// Validacion de tipo de correo al no haber destinatarios, si no se tienen destinatarios configurados
							// se registra en la bitacora como error
							if(correosEnviar.getInt("opc_alerta") == 0) {

								bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
								bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_CORREO_ADJUNTO_SIN_DESTINATARIOS;
								flagSinDestinatariosAdjunto = true;
								flagContinuarEnvio = false;
								esCorrecto= false;
							} else if (correosEnviar.getInt("opc_alerta") == 1) {
								bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
								bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_CORREO_ALERTA_SIN_DESTINATARIOS;
								flagSinDestinatariosAlerta = true;
								flagContinuarEnvio = false;
								esCorrecto= false;
							}
							// Si ambos tipos de correos no cuentan con destinatarios, se asigna un mensaje de error general.
							if (flagSinDestinatariosAlerta && flagSinDestinatariosAdjunto) {
								bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_CORREO_SIN_DESTINATARIOS;
								break;
							}
						}

						// Validar si se debe continuar con el envio de correo, en caso de que haya destinatarios activos
						if (flagContinuarEnvio) {
							// Por cada destinatario hara una iteracion para añadirlo a internetAddresses
							while (destinatarios.next()) {
								correoDestinatario += destinatarios.getString("des_destinatario");
								correoDestinatario +=",";
							}

							// quitamos la ultima coma
							if (correoDestinatario.length() > 0 && correoDestinatario.charAt(correoDestinatario.length() - 1) == ',') {
					        	correoDestinatario = correoDestinatario.substring(0, correoDestinatario.length() - 1);
					        }

							String archivos= "";

							// Agregar los destinatarios al recipiente de mensajes
							// Se asigna el asunto del correo
							sujetoCorreo = correosEnviar.getString("des_asunto");

							// Si no es una alerta, se adjuntan al correo los archivos generados
							if(correosEnviar.getInt("opc_alerta") == 0) {
								if(rutasArchivos.size() < 1 ) {
									ResultSet cuerpoSinArchivo = ConsultasCD.consultarCorreoSinArchivo(bitacora.interfaz);
									while (cuerpoSinArchivo.next()) {
										cuerpoCorreo = cuerpoSinArchivo.getString("des_cuerpo");
									}
								} else {
									cuerpoCorreo = correosEnviar.getString("des_cuerpo");
									// Iteramos los archivos generados

									for (int i = 0; i <= rutasArchivos.size() - 1; i++) {
										// Se crea el File(archivo) a partir de los archivos
										archivos +="{";
										if (i>0 && i <= rutasArchivos.size() - 2) {
											archivos +=",";
										}

										if (validarArchivoExiste(FilenameUtils.normalize(rutasArchivos.get(i)))) {


											// Leer el contenido del archivo como un array de bytes
									        byte[] fileContent = readFileToByteArray(rutasArchivos.get(i));
									        String pdfBase64 = Base64.encode(fileContent);
									        // Se crea cada parte que contendra archivos
									        archivos += "\"nombreArchivo\":\""+new File(rutasArchivos.get(i)).getName().trim()+'"' +',';
									        archivos += " \"archivo\":\""+ pdfBase64 +'"';
									        archivos += "}";
									        esCorrecto=true;
										} else {
											esCorrecto = false;
											// Si el archivo no existe, se registra en la bitacora como error
											bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
											bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_CORREO_ARCHIVO_INEXISTENTE;
											return;
										}
									}
								}
							} else {
								esCorrecto=true;
								cuerpoCorreo = correosEnviar.getString("des_cuerpo");
							}
							String tokenApiKey = tokenCorreoApi(false,servidorCorreo,puertoCorreo,pathApiTokenCorreo,usuarioApiCorreo,contrasenaApiCorreo,bitacora);
					        if(tokenApiKey == null || tokenApiKey == "") {
					        	throw new Exception(generalConstantes.mensajeErrorAPIToken(String.valueOf(404), tokenApiKey));
					        }
					        String mensajeCorreoParseado = convertirTextoAHtml(cuerpoCorreo);
					        String jsonInputString;
					        if (esCorrecto) {
					        	jsonInputString = "{    \"destinatario\": \""+correoDestinatario+"\",    \"asunto\": \""+sujetoCorreo+"\",    \"html\": \"<div>"+mensajeCorreoParseado+"</div>\",    \"archivos\": ["+archivos+"]}";
					        }
					        else {
					        	jsonInputString = "{    \"destinatario\": \""+correoDestinatario+"\",    \"asunto\": \""+sujetoCorreo+"\",    \"html\": \"<div>"+mensajeCorreoParseado+"</div>\"}";
					        }
					        HttpClient clienteHTTP = HttpClient.newHttpClient();
						    HttpResponse<String> peticion = clienteHTTP.send(
								HttpRequest
									.newBuilder(new URI("http://"+ servidorCorreo + ":" + puertoCorreo + pathApiCorreo))
									.header("Authorization",tokenApiKey)
									.header("APITOKEN",tokenApiKey)
									.header("Accept", "*/*")
									.header("Content-Type", "application/json")
									.POST(BodyPublishers.ofString(jsonInputString))
									.timeout(Duration.ofSeconds(10)) // Tiempo máximo de conexión // Establece un límite de tiempo de 10 segundos
									.build(),
								BodyHandlers.ofString()
							);

							int estatusRespuesta = peticion.statusCode();
							String respuesta = peticion.body();
							if (estatusRespuesta >= generalConstantes.CODIGO_ESTATUS_400) {
								throw new Exception(generalConstantes.mensajeErrorAPICredenciales(String.valueOf(estatusRespuesta), respuesta));
							} else if (respuesta.length() <= 0) {
								throw new Exception(generalConstantes.MENSAJE_API_CORREO_NO_ENVIADO);
							}
						}
					}
				} else {
					bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
					bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_CORREO_SIN_CORREOS;
				}
			} catch (Exception e) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = e.toString();
			}
		}

		private static String tokenCorreoApi(boolean tipoCorreo,String servidorCorreo, String puertoCorreo, String pathApiTokenCorreos, String usuarioApiCorreos, String contrasenaApiCorreos, BitacoraCLN bitacora) throws ClassNotFoundException, SecurityException, NullPointerException, SQLException, java.io.IOException {
			String tokenApiKey = null;
			String jsonInputString = "{    \"nom_usuario\": \""+usuarioApiCorreos+"\",    \"des_clave\": \""+contrasenaApiCorreos+"\"}";
		try {
	        HttpClient clienteHTTP = HttpClient.newHttpClient();
		    HttpResponse<String> peticion = clienteHTTP.send(
				HttpRequest
					.newBuilder(new URI("http://"+ servidorCorreo + ":" + puertoCorreo + pathApiTokenCorreos))
					.header("Accept", "*/*")
					.header("Content-Type", "application/json")
					.POST(BodyPublishers.ofString(jsonInputString))
					.timeout(Duration.ofSeconds(10)) // Tiempo máximo de conexión // Establece un límite de tiempo de 10 segundos
					.build(),
				BodyHandlers.ofString()
			);

			int estatusRespuesta = peticion.statusCode();
			String respuesta = peticion.body();
			tokenApiKey = respuesta;
			if (estatusRespuesta >= generalConstantes.CODIGO_ESTATUS_400) {
				throw new Exception(generalConstantes.mensajeErrorAPICredenciales(String.valueOf(estatusRespuesta), respuesta));
			} else if (respuesta.length() <= 0) {
				throw new Exception(generalConstantes.MENSAJE_API_CORREO_NO_ENVIADO);
			}
	        
		} catch (Exception e) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = e.toString();
	    	}
		return tokenApiKey;
		}

		private static byte[] readFileToByteArray(String filePath) throws IOException {
	        File file = new File(filePath);
	        FileInputStream fis = null;
			try {
				fis = new FileInputStream(file);
			} catch (FileNotFoundException e) {
				
			}
	        byte[] data = new byte[(int) file.length()];
	        try {
				fis.read(data);
			} catch (java.io.IOException e) {
				
			}
	        try {
				fis.close();
			} catch (java.io.IOException e) {
				
			}
	        return data;
	    }


	// Método para verificar si el archivo existe y no es un directorio
	public static boolean validarArchivoExiste(String cadena) {
		File fichero = null;
		fichero = new File(cadena);
		fichero.setExecutable(true, true);
		fichero.setReadable(true);
		fichero.setWritable(true);

		return fichero.exists() && !fichero.isDirectory() && fichero.canRead();
	}

	public static String convertirTextoAHtml(String texto) {
        StringBuilder htmlBuilder = new StringBuilder();

        // Escapar caracteres especiales de HTML
        texto = texto.replace("&", "&amp;")
                     .replace("<", "&lt;")
                     .replace(">", "&gt;")
                     .replace("\"", "&quot;")
                     .replace("'", "&#39;");

        // Reemplazar saltos de línea por <br>
        texto = texto.replaceAll("\n", "<br>");
        texto = texto.replaceAll("\t", "&nbsp;&nbsp;&nbsp;&nbsp;");

        // Agregar el texto convertido a HTML al StringBuilder
        htmlBuilder.append(texto);

        return htmlBuilder.toString();
    }

}
