package capaDeLogicaDeNegocios;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Iterator;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.OLE2NotOfficeXmlFileException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import utilidades.Utilidades;


public class XlsxCLN {

	// Metodo para realizar la conversion de archivos Excel a CSV
	public static void convertirArchivoExcelACSV(String ruta, String nombreArchivo, String extension, BitacoraCLN bitacora)
			throws Exception {

		Workbook libro = null;

		String rutaLocal = System.getProperty("user.dir");
		String rutaProceso = "/archivos/" + bitacora.interfaz.iduProceso;
		String rutaArchivoOrigen = rutaLocal;
		String rutaArchivoDestino = rutaLocal + rutaProceso;

		rutaArchivoOrigen += (ruta.isEmpty()) ? rutaProceso : "/" + ruta;

		Utilidades.crearDirectorio(rutaArchivoOrigen);
		Utilidades.crearDirectorio(rutaArchivoDestino);

		String archivoEntrada = rutaArchivoOrigen + "/" + nombreArchivo + extension;
		String archivoSalida = rutaArchivoDestino + "/" + nombreArchivo + ".csv";

		try {
			exportarXLSACSV(libro, archivoEntrada, archivoSalida, bitacora);
		} catch (Exception e) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = e.toString();
		}
	}

	// Metodo para crear el archivo CSV a partir de la clase dependiendo del archivo.
	private static void exportarXLSACSV(Workbook libro, String archivoEntrada, String archivoSalida, BitacoraCLN bitacora) throws IOException {
		Path rutaSeguraEntrada = Paths.get(archivoEntrada);
		if (!new File(rutaSeguraEntrada.toString()).getCanonicalPath().startsWith(generalConstantes.RUTA_RAIZ)) {
			throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
		}

		try {
			libro = new XSSFWorkbook(new FileInputStream(rutaSeguraEntrada.toString()));
			exportarXLSACSV(libro, archivoSalida);
		} catch (OLE2NotOfficeXmlFileException e) {
			libro = new HSSFWorkbook(new FileInputStream(rutaSeguraEntrada.toString()));
			exportarXLSACSV(libro, archivoSalida);
		}
	}

	// Metodo con la logica a realizar para la creacion del archivo CSV
	private static void exportarXLSACSV(Workbook libro, String archivoSalida) throws IOException {
		Path rutaSeguraSalida = Paths.get(archivoSalida);
		if (!new File(rutaSeguraSalida.toString()).getCanonicalPath().startsWith(generalConstantes.RUTA_RAIZ)) {
			throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
		}
		File archivo = new File(rutaSeguraSalida.toString());
		try (FileWriter archivoCSV = new FileWriter(archivo)) {

			archivo.setExecutable(false);
			archivo.setReadable(true);
			archivo.setWritable(true);

			Sheet hoja = libro.getSheetAt(0);
			Row fila;
			Iterator<Row> renglonIterador = hoja.iterator();

			if(Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {

				if(archivoSalida.toString().contains("DetalleAforeCoppel_")) {
					renglonIterador.next();
				}

				while (renglonIterador.hasNext()) {

					fila = renglonIterador.next();
					if(archivoSalida.toString().contains("DetalleAforeCoppel_")) {
						if(!renglonIterador.hasNext()) {
							break;
						}
					}
					Iterator<Cell> iteradorCeldas = fila.cellIterator();
					while (iteradorCeldas.hasNext()) {
						Cell celda = iteradorCeldas.next();
						String valorCelda = obtenerValorCelda(celda);

						if(valorCelda.length() != 0) {
							archivoCSV.append(valorCelda);
							if (iteradorCeldas.hasNext()) {
								archivoCSV.append(',');
							} else {
								archivoCSV.append('\n');
							}
						}
					}
				}
			}
			archivoCSV.close();
		} finally {
			if(libro != null) {
				libro.close();
			}
		}
	}

	// Metodo general para obtencion del valor de la celda del excel origen
	private static String obtenerValorCelda(Cell celda) {
		if (celda == null) {
			return "";
		}
		switch (celda.getCellType()) {
		case BOOLEAN:
			return String.valueOf(celda.getBooleanCellValue());
		case NUMERIC:
			if(DateUtil.isCellDateFormatted(celda)) {
				return String.valueOf(celda.getLocalDateTimeCellValue());
			} else {
				return String.valueOf(NumberToTextConverter.toText(celda.getNumericCellValue()));
			}
		case STRING:
			return celda.getStringCellValue();
		default:
			return "";
		}
	}
}