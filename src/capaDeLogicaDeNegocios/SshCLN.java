package capaDeLogicaDeNegocios;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.sql.rowset.CachedRowSet;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

import capaDeDatos.ProcesoCD;
import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import utilidades.JasyptEncriptacion;
import utilidades.Utilidades;

public class SshCLN {
	private static JasyptEncriptacion encriptacion = new JasyptEncriptacion();

	public static void EjecutarSH(String ipServidor, int puerto, String directorio, String usuarioSSH, String cntraSSH, String nombreArchivo, String extension, BitacoraCLN bitacora) throws Exception, IOException  {
		LogCLN log = new LogCLN();
		try {
		String usuario = encriptacion.decifrar(usuarioSSH);
		String clv = encriptacion.decifrar(cntraSSH);

        JSch jsch = new JSch();
        Session sesion = jsch.getSession(usuario, ipServidor, puerto);

        sesion.setPassword(clv);
        Channel canal = null;
        int estatus = 0;
        try {

        	// Obtiene parametros en caso de necesitar
        	CachedRowSet parametrosResult = ProcesoCD.obtenerParametros(bitacora.interfaz.iduPaso, Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"), bitacora);
        	String sParametros = "";
        	if(parametrosResult != null) {
        		while (parametrosResult.next()) {
        			if(parametrosResult.getString("nomparametro").equals("cntra") ||
        				parametrosResult.getString("nomparametro").equals("usuario")) {
        				sParametros = sParametros.concat(" "+ encriptacion.decifrar(parametrosResult.getString("desparametro")));
        			} else {
        				sParametros = sParametros.concat(" "+ parametrosResult.getString("desparametro"));
        			}
        		}
        	}

			// Configuracion para evitar la necesidad de la interacción del usuario
            java.util.Properties config = new java.util.Properties();
            config.put("PreferredAuthentications", "password");
            config.put("StrictHostKeyChecking", "no");
            sesion.setConfig(config);

            // Conexión al servidor SSH
            sesion.connect();

            // Canal SFTP
            canal = sesion.openChannel("sftp");
            canal.connect();
            ChannelSftp canalSftp = (ChannelSftp)canal;
            canalSftp.lstat(directorio + nombreArchivo + extension);
            canal.disconnect();
            canal = sesion.openChannel("exec");
            // Comando a ejecutar
            String comando = "sh "+ directorio + nombreArchivo + extension + sParametros;
            ((ChannelExec) canal).setCommand(comando);
            // Ejecutar el comando
            ByteArrayOutputStream errorBuffer = new ByteArrayOutputStream();
            InputStream err = canal.getExtInputStream();

            try {
            	canal.setOutputStream(System.out);
            	canal.connect();
            	byte[] tmp = new byte[1024];
            	while (true) {
            		while (err.available() > 0) {

            			if (Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
	            			int i = err.read(tmp, 0, 1024);
	            			if (i < 0) {
								break;
							}
	            			errorBuffer.write(tmp, 0, i);
            			}
            		}
            		if (canal.isClosed()) {
            			if ((err.available() > 0)) {
							continue;
						}
            			estatus = canal.getExitStatus();
            			break;
            		}
            	}
            } catch (Exception e) {
            	throw new IOException(e.toString());
			}

            // Cerrar la sesion SSH
            canal.disconnect();
            sesion.disconnect();

            if (estatus != 0) {
				throw new IOException(errorBuffer.toString("UTF-8"));
			}
        } catch (Exception e) {
        	if(canal!= null) {
				canal.disconnect();
			}

    		if(sesion != null) {
				sesion.disconnect();
			}

        	if (bitacora != null) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = e.toString();
			} else {
				log.grabarLog(e.toString());
				throw e;
			}
        }

        }
		catch(Exception e) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = e.toString();
			log.grabarLog(e.toString());
    	}
  }
}
