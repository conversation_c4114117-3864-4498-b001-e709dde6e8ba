package capaDeLogicaDeNegocios;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.text.StringEscapeUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import constantes.tiposPasosConstantes;
import utilidades.Utilidades;

public class ClienteSOAPCLN {

    public static void consumirServicioSoap(String url, String xml, BitacoraCLN bitacora) {
    	try {

    		int MAX_SIZE = 0;
    		Document documento;

            // Configuración de la conexión HTTP
    		URLEncoder.encode(url, "UTF-8");
            @SuppressWarnings("deprecation")
			URL servicio = new URL(url);
            HttpURLConnection conexion = (HttpURLConnection) servicio.openConnection();
            conexion.setReadTimeout(600000);
            conexion.setRequestMethod("POST");
            conexion.setRequestProperty("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
            conexion.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
            conexion.setRequestProperty("SOAPAction", "");
            conexion.setDoOutput(true);

            // Escribir la solicitud SOAP en el cuerpo de la solicitud
            try (OutputStream os = conexion.getOutputStream()) {
            	xml = sanitizarXML(xml);

                byte[] input = xml.getBytes("UTF-8");
                if(Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
                	os.write(input, 0, input.length);
    			}
            }

            // Leer la respuesta del servidor
            int codigoRespuesta = conexion.getResponseCode();
            if (codigoRespuesta == HttpURLConnection.HTTP_OK) {
                try (BufferedReader in = new BufferedReader(new InputStreamReader(conexion.getInputStream()))) {
                    String inputLinea;
                    StringBuilder respuesta = new StringBuilder();
                    while ((inputLinea = in.readLine()) != null) {
                        respuesta.append(inputLinea);
                        MAX_SIZE = respuesta.toString().length();
                    }

                    // Validar que la respuesta no venga vacia
                    if(respuesta.toString() != "") {

                        DocumentBuilderFactory factorDocumentoConstructor = DocumentBuilderFactory.newInstance();
                        factorDocumentoConstructor.setFeature(generalConstantes.LIBRERIA_DISALLOW_DOCTYPE_DEC, true);
                        factorDocumentoConstructor.setFeature(generalConstantes.LIBRERIA_EXTERNAL_GENERAL_ENTITIES, false);
                        factorDocumentoConstructor.setFeature(generalConstantes.LIBRERIA_EXTERNAL_PARAMETER_ENTITIES, false);
                        factorDocumentoConstructor.setFeature(generalConstantes.LIBRERIA_LOAD_EXTERNAL_DTD, false);
                        factorDocumentoConstructor.setNamespaceAware(true);

                        // Validacion para evitar manipulacion de variables vulnerables
                        if (respuesta.toString().length() <= MAX_SIZE) {
                        	DocumentBuilder constructorDocumento = factorDocumentoConstructor.newDocumentBuilder();
                            documento = constructorDocumento.parse(new ByteArrayInputStream(respuesta.toString().getBytes()));

                    	} else {
                    		throw new IOException(generalConstantes.EXCEPCION_MAXIMO_CARACTERES);
                    	}

                        // Navegar al elemento generarConciliacionReturn
                        Element obtenerElemento = (Element) documento.getElementsByTagName("generarConciliacionReturn").item(0);
                        if(obtenerElemento != null) {
                        	String textContent = obtenerElemento.getTextContent();
                            // Dividir los datos antes y después del caracter "|"
                            String[] parts = textContent.split("\\|");
                            if (parts.length == 2) {
                                String antesDelPipe = parts[0];
                                if(Integer.valueOf(antesDelPipe) != 0) {
                                	String despuesDelPipe = parts[1];
                                	bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
                                	bitacora.interfaz.mensajeError = generalConstantes.mensajeErrorServicio(antesDelPipe, despuesDelPipe);
                                }

                            } else {
                            	bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
                            	bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_CODIGO_RESPUESTA;
                            }
                        } else {
                        	bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
                        	bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_CODIGO_RESPUESTA;
                        }
                    }
                }
            } else {
            	bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
            	bitacora.interfaz.mensajeError =
            			generalConstantes.MENSAJE_ERROR_CODIGO_SOLICITUD
            			+ codigoRespuesta
            			+ " - " + conexion.getResponseMessage();
            }

            // Cerrar la conexión
            conexion.disconnect();
        } catch (Exception e) {
        	bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
        	bitacora.interfaz.mensajeError = tiposPasosConstantes.TIPO_CONSUMO_SERVICIO_SOAP + e.toString();
        }
    }

    //Metodo para sanitizar xml de entrada para consumo de servicios SOAP
	public static String sanitizarXML(String entrada) {
        entrada = entrada.replace("&", "&amp;");
        entrada = entrada.replace("<", "&lt;");
        entrada = entrada.replace(">", "&gt;");
        entrada = entrada.replace("\"", "&quot;");
        entrada = entrada.replace("'", "&apos;");
        return StringEscapeUtils.unescapeHtml4(entrada);
    }
}