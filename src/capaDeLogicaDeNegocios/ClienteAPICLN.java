package capaDeLogicaDeNegocios;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;

import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import entidades.ConexionInterfase;

public class ClienteAPICLN {

	static LogCLN log = new LogCLN();

	public static boolean EjecutarOpcionPLD (int iOpcion, String sFechaInicial, String sFechaFinal, int iNumeroAutorizo, String urlAPI, List<ConexionInterfase> conexiones, BitacoraCLN bitacora) throws IOException {

		boolean continua = false;

		try {
			//Instanciamos las clases de las variables
			ConexionInterfase varMoneygram = new ConexionInterfase();
			ConexionInterfase varEnvios = new ConexionInterfase();
			ConexionInterfase varPLD = new ConexionInterfase();

			//Recorremos valores de conexiones y asignamos a variables
			for (ConexionInterfase conexion : conexiones) {
				switch (conexion.db) {
					case "enviosmoneygram":
						conexion.tipo = "PostgreSQL";
						varMoneygram = conexion;
						break;
					case "enviosnacionales":
						conexion.tipo = "PostgreSQL";
						varEnvios = conexion;
						break;
					case "pld_coppel20160819":
						conexion.tipo = "SQLServer";
						varPLD = conexion;
						break;
				}
			}

			HttpClient clienteHTTP = HttpClient.newHttpClient();

			//Inicializa el request para enviar a la api.
			Map<String, Object> cuerpoPeticion = new HashMap<>();
			cuerpoPeticion.put("opcion",iOpcion);
			cuerpoPeticion.put("numeroEmpleado",iNumeroAutorizo);
			cuerpoPeticion.put("fechaInicial",sFechaInicial);
			cuerpoPeticion.put("fechaFinal",sFechaFinal);
			cuerpoPeticion.put("proveedorMoneyGram", varMoneygram.tipo);
			cuerpoPeticion.put("servidorMoneyGram", varMoneygram.servidor);
			cuerpoPeticion.put("bdMoneyGram", varMoneygram.db);
			cuerpoPeticion.put("userMoneyGram", varMoneygram.usuario);
			cuerpoPeticion.put("passMoneyGram", varMoneygram.clsinfo);
			cuerpoPeticion.put("proveedorNacionales", varEnvios.tipo);
			cuerpoPeticion.put("servidorNacionales", varEnvios.servidor);
			cuerpoPeticion.put("bdNacionales", varEnvios.db);
			cuerpoPeticion.put("userNacionales", varEnvios.usuario);
			cuerpoPeticion.put("passNacionales", varEnvios.clsinfo);
			cuerpoPeticion.put("proveedorPLD", varPLD.tipo);
			cuerpoPeticion.put("servidorPLD", varPLD.servidor);
			cuerpoPeticion.put("bdPLD", varPLD.db);
			cuerpoPeticion.put("userPLD", varPLD.usuario);
			cuerpoPeticion.put("passPLD", varPLD.clsinfo);

			ObjectMapper objectMapper = new ObjectMapper();
			String cuerpoJSON = objectMapper.writeValueAsString(cuerpoPeticion);
			HttpResponse<String> peticion = clienteHTTP.send(
				HttpRequest
					.newBuilder(new URI(urlAPI))
					.header("Accept", "*/*")
					.header("Content-Type", "application/json")
					.POST(BodyPublishers.ofString(cuerpoJSON))
					.build(),
				BodyHandlers.ofString()
			);

			//obtiene el codigo de estado HTTP
			int estatusRespuesta = peticion.statusCode();
			if (estatusRespuesta < generalConstantes.CODIGO_ESTATUS_400) {
				continua = true;
			} else if (estatusRespuesta >= generalConstantes.CODIGO_ESTATUS_400) {
				String respuesta = peticion.body();
				throw new Exception(generalConstantes.mensajeErrorAPIREST(String.valueOf(estatusRespuesta), respuesta));
			}
		} catch (Exception e) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_API + e.getMessage();
		}

		return continua;
	}
}
