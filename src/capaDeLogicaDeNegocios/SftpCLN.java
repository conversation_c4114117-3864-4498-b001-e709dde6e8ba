package capaDeLogicaDeNegocios;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.SQLException;

import org.apache.commons.io.FilenameUtils;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;

import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import utilidades.JasyptEncriptacion;

public class SftpCLN {
	private static JasyptEncriptacion encriptacion = new JasyptEncriptacion();

	// funcion para subir archivos
	public static void subirArchivoServidor(String ipDestino, String directorioDestino, String usuarioSftp, String cntraSftp, String nombreArchivo, String nombreArchivoDestino,String extension,int idBitacora, BitacoraCLN bitacora , Integer puerto)
			throws IOException, SQLException, JSchException, SftpException {
		subirArchivoServidor(ipDestino, directorioDestino, usuarioSftp, cntraSftp, nombreArchivo, nombreArchivoDestino, extension, String.valueOf(idBitacora), bitacora, puerto);
	}

	public static void subirArchivoServidor(String ipDestino, String directorioDestino, String usuarioSftp, String cntraSftp, String nombreArchivo, String nombreArchivoDestino,String extension,String carpeta, BitacoraCLN bitacora, Integer puerto)
			throws IOException, SQLException, JSchException, SftpException {
		LogCLN log = new LogCLN();
		//pasamos usuario y contraseña cifrada y la deciframos al asignar su valor
		String usuario = encriptacion.decifrar(usuarioSftp);
		String clv = encriptacion.decifrar(cntraSftp);
		JSch jsch = new JSch();
		FileInputStream is = null;
	    Session session = jsch.getSession(usuario, ipDestino, puerto);
	    session.setPassword(clv);
	    session.setConfig("PreferredAuthentications", "password");
	    session.setConfig("StrictHostKeyChecking", "no");
	    try {
		    session.connect();
		    ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
		    channelSftp.connect();
		    channelSftp.cd(directorioDestino);
			//creamos la conexion al servidor y nos situamos en el directorio a subir
			String nombreArchivoLocal = nombreArchivo + extension;
			// Rutas
			Path rutaSeguraDestino = Paths.get(System.getProperty("user.dir"), "archivos", String.valueOf(carpeta));
			Path rutaArchivoDestino = Paths.get(rutaSeguraDestino.toString(), nombreArchivoLocal);
		    Path rutaSegura = Paths.get( rutaArchivoDestino.toString());
		    File archivoServidor = new File(FilenameUtils.normalize(rutaSegura.toString()));
		    is = new FileInputStream(archivoServidor);
	        channelSftp.put( rutaSegura.toString(), nombreArchivoLocal);
		    // Establecer permisos después de subir el archivo
		} catch (Exception err) {
			if (bitacora != null) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = err.toString();
			} else {
				log.grabarLog(err.toString());
			}
		} finally {
			if (is != null) {
	            is.close();
		        session.disconnect();
	        }
		}
	}
	public static void bajarArchivoServidor(String ipOrigen, String directorioOrigen, String usuarioSftp, String cntraSftp, String nombreArchivoOrigen, String nombreArchivoDestino,String extension,int carpeta, Integer puerto, BitacoraCLN bitacora)
			throws IOException, SQLException, JSchException {
		LogCLN log = new LogCLN();
		//pasamos usuario y contraseña cifrada y la deciframos al asignar su valor
		String usuario = encriptacion.decifrar(usuarioSftp);
		String clv = encriptacion.decifrar(cntraSftp);

		JSch jsch = new JSch();
		//FileInputStream is = null;
	    Session session = jsch.getSession(usuario, ipOrigen, puerto);
	    session.setPassword(clv);
	    session.setConfig("PreferredAuthentications", "password");
	    session.setConfig("StrictHostKeyChecking", "no");
	    try {
	    	boolean created =true;
	    	//creamos la conexion al servidor y nos situamos en el directorio a subir
	    	String nombreArchivoLocal = nombreArchivoDestino + extension;
			// Rutas
			Path rutaSeguraDestino = Paths.get(System.getProperty("user.dir"), "archivos", String.valueOf(carpeta));
			String directoryPath = rutaSeguraDestino.toString();
	        File directory = new File(directoryPath);
			if (!directory.exists()) {
	            created = directory.mkdirs(); // Usa mkdirs() para crear directorios intermedios si es necesario
	            if(!directory.exists()) {
	            	created = false;
	            }
			}
			if(created) {
				Path rutaArchivoDestino = Paths.get(rutaSeguraDestino.toString(), nombreArchivoLocal);
			    Path rutaSegura = Paths.get( rutaArchivoDestino.toString());
			    session.connect();
			    ChannelSftp channelSftp = (ChannelSftp) session.openChannel("sftp");
			    channelSftp.connect();
			    channelSftp.cd(directorioOrigen);
			    String DirectorioArchivoServidor=directorioOrigen +"/"+ nombreArchivoOrigen + extension;
			    // obtiene el archivo del servidor
			    channelSftp.get( DirectorioArchivoServidor ,rutaSegura.toString());
	            File file = new File(rutaSegura.toString());
			    if (file.exists()) {
	                // Establecer permisos de lectura y escritura para el propietario, y solo lectura para otros
			    	file.setReadable(true, true);
			    	file.setWritable(true, true);
			    	file.setExecutable(true, true);
	            }
			}
			else {
				throw new Exception(generalConstantes.MENSAJE_ERROR_GENERAR_RUTA + directory.toString());
			}
		} catch (Exception err) {
			if (bitacora != null) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = err.toString();
			} else {
				log.grabarLog(err.toString());
			}
			if (session != null) {
		        session.disconnect();
	        }
		} finally {
			if (session != null) {
		        session.disconnect();
	        }
		}
	}
}
