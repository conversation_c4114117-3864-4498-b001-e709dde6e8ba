package capaDeLogicaDeNegocios;

import java.io.IOException;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.sql.rowset.CachedRowSet;

import com.fasterxml.jackson.databind.ObjectMapper;

import capaDeDatos.ConexionCD;
import capaDeDatos.ProcesoCD;
import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import constantes.tiposPasosConstantes;
import entidades.BitacoraInterfase;
import entidades.ConexionInterfase;
import entidades.PasoInterfase;
import utilidades.Utilidades;


public class ProcesoCLN {
	// Atributos principales
	public int idProceso;
	public String idUsuario;
	public String tipoEjecucion;
	private int tipoPaso;
	static LogCLN log = new LogCLN();
	private static String fechaActual = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

	// Constructor de la clase
	public ProcesoCLN(int proceso, String usuario, String tipo) {
		this.idProceso = proceso;
		this.idUsuario = usuario;
		this.tipoEjecucion = tipo;
	}

	// Metodo para validar si el proceso es valido para la ejecucion
	public boolean validarProceso() throws IOException, SQLException {
		return ProcesoCD.validarProceso(this.idProceso);
	}

	// Metodo principal para la ejecucion de todos los procesos configurados
	public void iniciarProceso() throws Exception {
		try {
			List<String> listaRutasArchivos = new ArrayList<>();

			// Se crea variable para guardar el resultado de las ejecuciones de scripts de
			// los pasos
			// para cuando es necesario realizar algo con la informacion en un paso
			// posterior.
			List<PasoInterfase> rsPasos = new ArrayList<>();

			// Inicia la bitacora del proceso
			BitacoraCLN bitacora = new BitacoraCLN();
			bitacora.interfaz = new BitacoraInterfase(this.idProceso, this.idUsuario);
			bitacora.iniciarBitacora(Integer.valueOf(this.tipoEjecucion));

			// Se consulta la logica a seguir del proceso en ejecucion
			CachedRowSet logica = ProcesoCD.obtenerLogicaProceso(this.idProceso, bitacora.interfaz.iduBitacora);
			if (!logica.isBeforeFirst()) {
				bitacora.actualizarEstatusBitacora(estatusConciliacionConstantes.ERROR, generalConstantes.MENSAJE_ERROR_FALTA_LOGICA);
				return;
			}
			// Se realiza la ejecucion de la logica del proceso
			while (logica.next()) {
				int idPaso = logica.getInt("idpaso");
				this.tipoPaso = logica.getInt("idtipo");
				bitacora.interfaz.iduEtapa = logica.getInt("idetapa");
				bitacora.interfaz.iduPaso = idPaso;
				bitacora.actualizarEstatusBitacoraDetalle(estatusConciliacionConstantes.PENDIENTE,
						logica.getString("despaso"));
				String mensajeExitoPaso = "";
				String scriptPaso = logica.getString("sfunsql");
				// Si no es un paso que haga ejecucion de su script asignado en la tabla de pasos, se utiliza dicho script
				// para validar si el paso debe ejecutarse o no
				boolean pasoAutorizado = false;
				try {
					ArrayList<Integer> tiposNoValidar = new ArrayList<>();
					tiposNoValidar.add(tiposPasosConstantes.TIPO_EJECUTAR_SCRIPT);
					tiposNoValidar.add(tiposPasosConstantes.TIPO_GENERAR_PDF);
					tiposNoValidar.add(tiposPasosConstantes.TIPO_EJECUTAR_SCRIPT_CON_XML);
					if (!tiposNoValidar.contains(this.tipoPaso) && !scriptPaso.isEmpty()) {
						CachedRowSet resultadoScriptValidacion = ProcesoCD.ejecutarScriptPaso(scriptPaso, logica.getInt("clvconexion"), bitacora);

						if(resultadoScriptValidacion.isBeforeFirst()) {
							resultadoScriptValidacion.next();
							pasoAutorizado = resultadoScriptValidacion.getInt(1) == 1;
							if (!pasoAutorizado && resultadoScriptValidacion.getMetaData().getColumnCount() > 1) {
								mensajeExitoPaso = resultadoScriptValidacion.getString(2);
							}
						} else {
							throw new Exception(generalConstantes.MENSAJE_ERROR_VALIDACION_PASO_SIN_RESULTADOS);
						}
					} else {
						pasoAutorizado = true;
					}
				} catch (Exception e) {
					bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_GENERAR_ARCHIVO_PDF;

					bitacora.actualizarEstatusBitacora(estatusConciliacionConstantes.ERROR, generalConstantes.MENSAJE_ERROR_PROCESO);
					bitacora.actualizarEstatusBitacoraDetalle(estatusConciliacionConstantes.ERROR, generalConstantes.mensajeErrorValidarPaso(e.getMessage()));
					return;
				}

				if (pasoAutorizado) {
					switchPasos: switch (this.tipoPaso) {
					// CASE: Ejecucion de scripts SQL de paso en ejecucion
					case tiposPasosConstantes.TIPO_EJECUTAR_SCRIPT:
						CachedRowSet resultadoScript = ProcesoCD.ejecutarScriptPaso(scriptPaso, logica.getInt("clvconexion"), bitacora);
						// Si el result set es nulo, significa que algo fallo
						if (resultadoScript == null) {
							break;
						}

						// Si el script ejecutado no retorno resultados, se registra en la bitacora.
						if (!resultadoScript.isBeforeFirst()) {
							mensajeExitoPaso = generalConstantes.MENSAJE_PASO_EXITOSO_SIN_RESULTADOS;
						}

						rsPasos.add(new PasoInterfase(idPaso, resultadoScript));

						break;

					// CASE: Generacion de archivo EXCEL
					case tiposPasosConstantes.TIPO_GENERAR_EXCEL:
						String rutaArchivoExcel = logica.getString("ruta");
						String nombreArchivoExcel = logica.getString("nomarchivo") + logica.getString("extension");
						String rutaArchivoExcelGenerado = GeneracionArchivosCLN.generarArchivoExcel(idProceso, rsPasos,
								rutaArchivoExcel, nombreArchivoExcel, bitacora);
						if (bitacora.interfaz.iduEstatus != estatusConciliacionConstantes.ERROR) {
							if (rutaArchivoExcelGenerado != null) {
								// Un -1 indica que no habia informacion en los pasos para llenar el excel y ya
								// se marco como error para la bitacora
								if (!rutaArchivoExcelGenerado.equals("-1")) {
									listaRutasArchivos.add(rutaArchivoExcelGenerado);
									bitacora.interfaz.rutaArchivoSalida = rutaArchivoExcelGenerado;
								}
							} else {
								bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
								bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_GENERAR_ARCHIVO_EXCEL;
							}
						}
						break;

					// CASE: Generacion e insercion en archivo TXT
					case tiposPasosConstantes.TIPO_GENERAR_TXT:
						String rutaArchivoTexto = logica.getString("ruta");
						String nombreArchivoTexto = logica.getString("nomarchivo") + logica.getString("extension");
						String rutaArchivoTextoGenerado = GeneracionArchivosCLN.generarArchivoTexto(rsPasos.get(0).rsDatos, rutaArchivoTexto, nombreArchivoTexto, bitacora);
						if (bitacora.interfaz.iduEstatus != estatusConciliacionConstantes.ERROR) {
							if (rutaArchivoTextoGenerado != null) {
								// Un -1 indica que no habia informacion en los pasos para llenar el archivo y
								// ya se marco como error para la bitacora
								if (!rutaArchivoTextoGenerado.equals("-1")) {
									listaRutasArchivos.add(rutaArchivoTextoGenerado);
									bitacora.interfaz.rutaArchivoSalida = rutaArchivoTextoGenerado;
								}
							} else {
								bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
								bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_GENERAR_ARCHIVO_TEXTO;
							}
						}

						break;

					// CASE Envio de correo
					case tiposPasosConstantes.TIPO_ENVIAR_CORREO:
						EnvioCorreoCLN.enviarCorreosConciliacion(listaRutasArchivos, this.idProceso, idPaso, bitacora);
						break;

					// CASE: Subir archivo mediante protocolo SFTP
					case tiposPasosConstantes.TIPO_SUBIR_ARCHIVO:
						String ipSubir = logica.getString("servidor");
						String usuarioSftp = logica.getString("usuario");
						String rutaSubir = logica.getString("ruta");
						int puertoSftp = logica.getInt("puerto");
						String cntraSftp = logica.getString("cntra");
						String nombreArchivoSubir = logica.getString("nomarchivo");
						String nombreArchivoDestino = logica.getString("nomarchivodestino");
						String extensionSubir = logica.getString("extension");

						if (nombreArchivoDestino.equals("")) {
							nombreArchivoDestino = nombreArchivoSubir;
						}

						SftpCLN.subirArchivoServidor(ipSubir, rutaSubir, usuarioSftp, cntraSftp, nombreArchivoSubir,
								nombreArchivoDestino, extensionSubir, this.idProceso, bitacora, puertoSftp);

						break;

					// CASE: Bajar archivo mediante protocolo SAMBA
					case tiposPasosConstantes.TIPO_BAJAR_ARCHIVO:
					/**
					 * Se comenta dado que ya no se utilizará esta funcionalidad
					 * No se elimina en caso de que se vuelva a necesitar samba a futuro
					 * 
						String ipBajar = logica.getString("servidor");
						String rutaBajar = logica.getString("ruta");
						String usuarioSamba = logica.getString("usuario");
						String cntraSamba = logica.getString("cntra");
						String nombreArchivoBajar = logica.getString("nomarchivo");
						String extensionBajar = logica.getString("extension");
						boolean enviarArchivoPorCorreo = logica.getBoolean("opcenviarporcorreo");

						String rutaAbsolutaArchivoDescargado = SambaCLN.bajarArchivoServidor(ipBajar, rutaBajar,
								usuarioSamba, cntraSamba, nombreArchivoBajar, extensionBajar, this.idProceso, bitacora);
						if (enviarArchivoPorCorreo && !rutaAbsolutaArchivoDescargado.equals("")) {
							listaRutasArchivos.add(rutaAbsolutaArchivoDescargado);
							bitacora.interfaz.rutaArchivoSalida = rutaAbsolutaArchivoDescargado;
						}
					*/
						break;

					// CASE: consumo de servicio SOAP
					case tiposPasosConstantes.TIPO_CONSUMO_SERVICIO_SOAP:
						ClienteSOAPCLN.consumirServicioSoap(logica.getString("servidor"), logica.getString("ruta"), bitacora);
						break;

					// CASE: convertir archivo xlsx a csv
					case tiposPasosConstantes.TIPO_CONVERTIR_XLSX_EN_CSV:
						XlsxCLN.convertirArchivoExcelACSV(logica.getString("ruta"), logica.getString("nomarchivo"),
								logica.getString("extension"), bitacora);
						break;

					// CASE: Generacion de archivo PDF
					case tiposPasosConstantes.TIPO_GENERAR_PDF:
						String rutaArchivoPDF = logica.getString("ruta");
						String nombreArchivoPDF = logica.getString("nomarchivo") + logica.getString("extension");
						String rutaArchivoPDFGenerado = GeneracionArchivosCLN.generarPDF(idProceso, scriptPaso,
								rutaArchivoPDF, nombreArchivoPDF, bitacora);
						if (bitacora.interfaz.iduEstatus != estatusConciliacionConstantes.ERROR) {
							if (rutaArchivoPDFGenerado != null) {
								// Un -1 indica que no habia informacion en los pasos para llenar el excel y ya
								// se marco como error para la bitacora
								if (!rutaArchivoPDFGenerado.equals("-1")) {
									listaRutasArchivos.add(rutaArchivoPDFGenerado);
									bitacora.interfaz.rutaArchivoSalida = rutaArchivoPDFGenerado;
								}
							} else {
								bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
								bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_GENERAR_ARCHIVO_PDF;
							}
						}
						break;
					case tiposPasosConstantes.TIPO_EJECUTAR_SH:
						String ip = logica.getString("servidor");
						String ruta = logica.getString("ruta");
						String usuario = logica.getString("usuario");
						String cntraSSH = logica.getString("cntra");
						String nombreArchivo = logica.getString("nomarchivo");
						String extension = logica.getString("extension");
						int puerto = logica.getInt("puerto");
						SshCLN.EjecutarSH(ip, puerto, ruta, usuario, cntraSSH, nombreArchivo, extension, bitacora);
						break;
					case tiposPasosConstantes.TIPO_COPIAR_ARCHIVO:
						String rutaOrigen = logica.getString("ruta") + logica.getString("nomarchivo") + logica.getString("extension");
						String rutaDestinoCompleta = logica.getString("rutadestino") + logica.getString("nomarchivodestino") + logica.getString("extension");
						Utilidades.crearDirectorio(logica.getString("rutadestino"));
						Utilidades.realizarCopiadoDeArchivo(rutaOrigen, rutaDestinoCompleta, bitacora);
						break;
					case tiposPasosConstantes.TIPO_BAJAR_ARCHIVO_SFTP:
						String ipBajarSFTP = logica.getString("servidor");
						String rutaBajarSFTP = logica.getString("ruta");
						String usuarioBajarSFTP = logica.getString("usuario");
						String cntraBajarSFTP = logica.getString("cntra");
						String nombreArchivoBajarSFTP = logica.getString("nomarchivo");
						String nombreArchivoDestinoSFTP = logica.getString("nomarchivodestino");
						String extensionBajarSFTP = logica.getString("extension");
						int puertoBajarSFTP = logica.getInt("puerto");
						
						if (nombreArchivoDestinoSFTP.equals("")) {
							nombreArchivoDestinoSFTP = nombreArchivoBajarSFTP;
						}

						SftpCLN.bajarArchivoServidor(ipBajarSFTP, rutaBajarSFTP,
								usuarioBajarSFTP, cntraBajarSFTP, nombreArchivoBajarSFTP, nombreArchivoDestinoSFTP, extensionBajarSFTP, this.idProceso,puertoBajarSFTP ,bitacora);

						break;

					// CASE: Consumo de web service wsReplicadorPLD.
					case tiposPasosConstantes.TIPO_CONSUMO_WS_REPLICADOR_PLD:
						String sFechaInicial = fechaActual;
						String sFechaFinal = fechaActual;
						int idUsuario = Integer.parseInt(this.idUsuario);
						ArrayList<Integer> categoriasApi = new ArrayList<>();

						Properties config = Utilidades.obtenerPropiedades();
						categoriasApi.add(Integer.valueOf(config.getProperty("categorias.enviosmoneygram")));
						categoriasApi.add(Integer.valueOf(config.getProperty("categorias.enviosnacionales")));
						categoriasApi.add(Integer.valueOf(config.getProperty("categorias.pld")));

						ConexionCD conexionCD = new ConexionCD();
						ArrayList<ConexionInterfase> conexiones = conexionCD.obtenerListaDatosConexion(categoriasApi);

						CachedRowSet logicaParametros =
								ProcesoCD.obtenerParametrosApiRest(idPaso, Integer.valueOf(config.getProperty("categorias.ingresos")), bitacora);
						//Se obtiene ruta de ApiRest.
						String sUrlapi = logica.getString("servidor");

						// Recorre opciones para consumo de api.
						while (logicaParametros.next()) {
							int iOpcion = logicaParametros.getInt("desparametro");
							ClienteAPICLN.EjecutarOpcionPLD(iOpcion, sFechaInicial, sFechaFinal, idUsuario, sUrlapi, conexiones, bitacora);
						}

						break;

					// CASE: Ejecucion de scripts SQL reemplazando XML
					case tiposPasosConstantes.TIPO_EJECUTAR_SCRIPT_CON_XML:
						String xmlReemplazar = rsPasos.get(rsPasos.size() - 1).xmlDatos;
						if (xmlReemplazar == null || xmlReemplazar.isEmpty()) {
							bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
							bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_SIN_INFORMACION_PASO_ANTERIOR;
							break;
						}

						String funSql = scriptPaso.replace("@xml;", "'" + xmlReemplazar + "'");
						CachedRowSet resultadoScriptXML = ProcesoCD.ejecutarScriptPaso(funSql, logica.getInt("clvconexion"), bitacora);
						// Si el result set es nulo, significa que algo fallo
						if (resultadoScriptXML == null) {
							break;
						}

						// Si el script ejecutado no retorno resultados, se registra en la bitacora.
						if (!resultadoScriptXML.isBeforeFirst()) {
							mensajeExitoPaso = generalConstantes.MENSAJE_PASO_EXITOSO_SIN_RESULTADOS;
							break;
						}

						resultadoScriptXML.next();
						rsPasos.add(new PasoInterfase(idPaso, resultadoScriptXML.getString(1)));

						break;
						
					// CASE: Ejecucion de MS-PLD por peticion HTTP POST
					case tiposPasosConstantes.TIPO_EJECUTAR_API_MSPLD:
						int categoriaIngresos = Integer.valueOf(Utilidades.obtenerPropiedades().getProperty("categorias.ingresos"));
						String token = ClienteAPICLN.ObtenerTokenMSPLD(bitacora);
						
						// Si el token es nulo significa que hubo un error y ya se registró en la bitacora
						if (token == null) {
							break;
						}
						
						// Asignamos el token al cabecero
						Map<String,String> cabecerosPeticion = new HashMap<>();
						cabecerosPeticion.put("AUTHORIZATION", token);
						
						// Obtenemos los parametros del body
						CachedRowSet cuerpoPeticionBd = ProcesoCD.obtenerParametrosApiRest(idPaso, categoriaIngresos, bitacora);
						Map<String, Object> cuerpoPeticion = new HashMap<>();
						
						while (cuerpoPeticionBd.next()) {
							String nomParametro = cuerpoPeticionBd.getString("nomparametro");
							String desParametro = cuerpoPeticionBd.getString("desparametro");
							String tipoDato = cuerpoPeticionBd.getString("tipodato");
							String xmlReemplazo = "";
							
							// Si alguno de los parametros del body requiere el xml del paso anterior
							// se valida que dicho xml no venga vacío
							if (desParametro.contains("@xml;")) {
								xmlReemplazo = rsPasos.get(rsPasos.size() - 1).xmlDatos;
								if(xmlReemplazo == null || xmlReemplazo.isBlank()) {
									bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
									bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_SIN_INFORMACION_PASO_ANTERIOR;
									break switchPasos;
								}
							}
							
							// Se reemplaza el xml y se asigna la propiedad al body de la peticion segun su tipo de dato
							switch (tipoDato) {
							case "String[]":
								String[] parametroListaStrings = desParametro.split("\\|");
								for (int i = 0; i < parametroListaStrings.length; i++) {
									if (parametroListaStrings[i].contains("@xml;")) {
										parametroListaStrings[i] = parametroListaStrings[i].replace("@xml;", "'" + xmlReemplazo + "'");
									}
								}
								cuerpoPeticion.put(nomParametro, parametroListaStrings);
								break;
							default:
								cuerpoPeticion.put(nomParametro, desParametro.replace("@xml;", "'" + xmlReemplazo + "'"));
								break;
							}
						}

						String sUrlApi = logica.getString("servidor");
						String respuestaJSON = ClienteAPICLN.EjecutarPeticionHTTP(sUrlApi, generalConstantes.POST, cabecerosPeticion, cuerpoPeticion, bitacora);
						
						// Si la respuesta es nula significa que hubo un error y ya se registró en la bitacora
						if (respuestaJSON == null) {
							break;
						}
						
						@SuppressWarnings("unchecked")
						Map<String,Object> respuestaMap = new ObjectMapper().readValue(respuestaJSON, HashMap.class);
						@SuppressWarnings("unchecked")
						ArrayList<String> datos = (ArrayList<String>) respuestaMap.get("data");
						// Si la peticion retorna informacion, se guarda para poder ser usada en el proximo paso
						if (datos != null && datos.size() > 0) {
							String respuesta = datos.get(0);
							rsPasos.add(new PasoInterfase(idPaso, respuesta));
						}
						
						break;
					}
				}

				if (bitacora.interfaz.iduEstatus == estatusConciliacionConstantes.ERROR) {
					bitacora.actualizarEstatusBitacoraDetalle(estatusConciliacionConstantes.ERROR, bitacora.interfaz.mensajeError);

					// Cuando solo falla el envio de correo, la ejecucion es exitosa
					if (this.tipoPaso != tiposPasosConstantes.TIPO_ENVIAR_CORREO) {
						bitacora.actualizarEstatusBitacora(estatusConciliacionConstantes.ERROR, generalConstantes.MENSAJE_ERROR_PROCESO);
						return;
					}

				} else {
					bitacora.actualizarEstatusBitacoraDetalle(estatusConciliacionConstantes.EXITOSO, mensajeExitoPaso);
				}
			}

			bitacora.actualizarEstatusBitacora(estatusConciliacionConstantes.EXITOSO, generalConstantes.MENSAJE_PROCESO_EXITOSO);

		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorEjecutarProceso(idProceso, e.getMessage()));
		}
	}
}
