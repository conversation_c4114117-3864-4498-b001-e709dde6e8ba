package capaDeLogicaDeNegocios;

import java.io.IOException;
import java.sql.SQLException;

import capaDeDatos.BitacoraCD;
import entidades.BitacoraInterfase;

public class BitacoraCLN {

	public BitacoraInterfase interfaz;

	// Metodo para grabar el estatus del proceso ejecucion en la bitacora
	public void iniciarBitacora(int tipoEjecucion) throws IOException, SQLException {
		if(this.interfaz != null) {
			BitacoraCD.iniciarBitacora(this.interfaz, tipoEjecucion);
		}
	}

	// Metodo para actualizar estatus del proceso ejecucion en la bitacora
	public void actualizarEstatusBitacora(int estatus, String mensaje) throws IOException, SQLException {
		if(this.interfaz != null) {
			BitacoraCD.actualizarEstatusBitacora(estatus, mensaje, this.interfaz);
		}
	}

	// Metodo para grabar o actualizar el estatus del paso en ejecucion en el detalle de la bitacora
	public void actualizarEstatusBitacoraDetalle(int estatus, String mensaje) throws IOException, SQLException {
		if(this.interfaz != null) {
			BitacoraCD.actualizarEstatusBitacoraDetalle(estatus, mensaje, this.interfaz);
		}

	}
}
