package capaDeLogicaDeNegocios;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermissions;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.sql.rowset.CachedRowSet;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;

import capaDeDatos.ConsultasCD;
import capaDeDatos.GeneracionArchivosCD;
import constantes.estatusConciliacionConstantes;
import constantes.estilosExcelConstantes;
import constantes.generalConstantes;
import entidades.PasoInterfase;
import utilidades.Utilidades;


public class GeneracionArchivosCLN {
	private static String raizDesignada = Paths
			.get(System.getProperty("user.dir"), generalConstantes.RUTA_RAIZ_ARCHIVOS_GENERADOS).toString();

	// Genera el archivo de texto bajo el nombre y ruta especificados concatenando
	// cada columna del ResultSet en un renglon
	public static String generarArchivoTexto(CachedRowSet datos, String rutaArchivo, String nombreArchivo,
			BitacoraCLN bitacora) throws IOException {
		String rutaAbsolutaArchivo = null;
		OutputStreamWriter escritor = null;
		OutputStream lineaSalida = null;
		try {
			// Se valida que exista informacion para llenar el archivo, de no existir, se
			// termina el proceso y se registra en bitacora
			if (!datos.isBeforeFirst()) {
				return "-1";
			}

			// Si la ruta no existe, la crea
			Path rutaSeguraCarpeta = Paths.get(generalConstantes.RUTA_RAIZ_ARCHIVOS_GENERADOS, rutaArchivo);
			File carpetaArchivo = new File(rutaSeguraCarpeta.toString());
			carpetaArchivo.setExecutable(true, true);
			carpetaArchivo.setReadable(true);
			carpetaArchivo.setWritable(true);
			// Si la ruta del archivo intenta salir de su raiz designada, detiene el proceso
			// y lo marca como error en la bitacora
			if (!carpetaArchivo.getCanonicalPath().startsWith(raizDesignada)) {
				throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
			}
			carpetaArchivo.mkdirs();
			Files.setPosixFilePermissions(rutaSeguraCarpeta, PosixFilePermissions.fromString("rwxr--r--"));

			Path rutaSeguraArchivo = Paths.get(generalConstantes.RUTA_RAIZ_ARCHIVOS_GENERADOS, rutaArchivo,
					nombreArchivo);
			File archivo = new File(rutaSeguraArchivo.toString());
			archivo.setExecutable(true, true);
			archivo.setReadable(true);
			archivo.setWritable(true);
			// Si la ruta del archivo intenta salir de su raiz designada, detiene el proceso
			// y lo marca como error en la bitacora
			if (!archivo.getCanonicalPath().startsWith(raizDesignada)) {
				throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
			}
			lineaSalida = new FileOutputStream(archivo);
			escritor = new OutputStreamWriter(lineaSalida);

			ResultSetMetaData metadata = datos.getMetaData();
			int cantidadColumnas = metadata.getColumnCount();
			while (datos.next()) {
				String renglon = "";
				// Se itera cada columna para concatenarlas en un solo renglon
				for (int col = 1; col <= cantidadColumnas; col++) {
					String valor = datos.getString(col);
					if (valor != null) {
						// Si no es la primera columna, se agrega una coma al inicio
						if (col > 1) {
							renglon += ",";
						}

						renglon += datos.getString(col);
					}
				}

				// Se agrega el concatenado generado al archivo
				if (Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
					escritor.write(renglon + "\n");
				}
			}

			// Se finaliza la captura del archivo y se retorna su ruta absoluta.
			Files.setPosixFilePermissions(rutaSeguraArchivo, PosixFilePermissions.fromString("rwxr--r--"));
			rutaAbsolutaArchivo = archivo.getAbsolutePath();
		} catch (Exception e) {
			rutaAbsolutaArchivo = null;
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = e.toString();
		} finally {
			if (escritor != null) {
				escritor.close();
			}
			if (lineaSalida != null) {
				lineaSalida.close();
			}
		}

		return rutaAbsolutaArchivo;
	}

	// Genera un archivo de excel en la ruta especificada segun la configuracion en
	// bd de la conciliacion actual
	// se debe pasar un ResultSet en la lista por cada tabla a llenar en el excel
	public static String generarArchivoExcel(int idConciliacion, List<PasoInterfase> listaDatos, String rutaArchivo,
			String nombreArchivo, BitacoraCLN bitacora) throws IOException {
		String rutaAbsolutaArchivo = null;
		XSSFWorkbook excel = new XSSFWorkbook();
		try {
			// Se valida que exista informacion para todas las tablas a generar, de no
			// existir, se termina el proceso y se registra en bitacora
			CachedRowSet tablasValidar = GeneracionArchivosCD.obtenerTablasExcelPorConciliacion(idConciliacion);
			boolean existeInformacion = false;
			// Si no existen tablas para generar el excel, se salta la validacion de
			// informacion
			if (!tablasValidar.isBeforeFirst()) {
				existeInformacion = true;
			} else {
				while (tablasValidar.next()) {
					int idPaso = tablasValidar.getInt("idu_conciliacion_paso");
					CachedRowSet informacionTabla = listaDatos.stream().filter(item -> {
						return item.idPaso == idPaso;
					}).collect(Collectors.toList()).get(0).rsDatos;

					if (informacionTabla.isBeforeFirst()) {
						existeInformacion = true;
						break;
					}
				}
			}

			if (!existeInformacion) {
				return "-1";
			}

			// Obtiene las páginas que contendrá el excel
			CachedRowSet paginas = GeneracionArchivosCD.obtenerPaginasExcelPorConciliacion(idConciliacion);
			if (paginas == null) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_OBTENER_PAGINAS_EXCEL;
				return null;
			}

			// Si no hay páginas, se registra como error y termina el proceso
			if (!paginas.next()) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_EXCEL_SIN_PAGINAS;
				return null;
			}
			paginas.beforeFirst();

			// Si la ruta para el archivo no existe, la crea
			Path rutaSeguraCarpeta = Paths.get(generalConstantes.RUTA_RAIZ_ARCHIVOS_GENERADOS, rutaArchivo);
			File carpetaArchivo = new File(rutaSeguraCarpeta.toString());
			carpetaArchivo.setExecutable(true, true);
			carpetaArchivo.setReadable(true);
			carpetaArchivo.setWritable(true);
			// Si la ruta del archivo intenta salir de su raiz designada, detiene el proceso
			// y lo marca como error en la bitacora
			if (!carpetaArchivo.getCanonicalPath().startsWith(raizDesignada)) {
				throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
			}
			carpetaArchivo.mkdirs();
			Files.setPosixFilePermissions(rutaSeguraCarpeta, PosixFilePermissions.fromString("rwxr--r--"));

			// Se crea el arhcivo de excel
			Path rutaSeguraArchivo = Paths.get(generalConstantes.RUTA_RAIZ_ARCHIVOS_GENERADOS, rutaArchivo,
					nombreArchivo);
			File archivo = new File(rutaSeguraArchivo.toString());
			archivo.setExecutable(true, true);
			archivo.setReadable(true);
			archivo.setWritable(true);
			// Si la ruta del archivo intenta salir de su raiz designada, detiene el proceso
			// y lo marca como error en la bitacora
			if (!archivo.getCanonicalPath().startsWith(raizDesignada)) {
				throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
			}

			// Indice de la última columna con información del archivo
			// Se utiliza para iterar todas las columnas y ajustar sus tamaños
			int columnaMaxima = 0;

			/***************************************
			 * GENERACION DE PAGINAS
			 **********************************************/
			// Iteramos cada pagina que contendra el excel
			while (paginas.next()) {
				// Contador utilizado para saber el indice del próximo renglón a crear
				int cantidadFilas = 0;
				XSSFSheet pagina = excel.createSheet(paginas.getString("nom_pagina"));

				// Variables para almacenar el estilo de la iteracion anterior
				// Se utiliza para agilizar el proceso en caso de repetir muchas veces el mismo
				// estilo
				int idEstiloAnterior = 0;
				XSSFCellStyle estiloAnterior = null;

				int columnaActual = 0;

				/***************************************
				 * GENERACION ENCABEZADOS
				 **********************************************/
				cantidadFilas = crearAnotacionesExcel(cantidadFilas, excel, pagina,
						paginas.getInt("idu_conciliacion_excel_pagina"), true, bitacora);
				if (cantidadFilas < 0) {
					if (cantidadFilas == -1) {
						bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_OBTENER_ENCABEZADOS_EXCEL;
					}

					return null;
				}

				/***************************************
				 * GENERACION TABLAS
				 **********************************************/
				CachedRowSet tablas = GeneracionArchivosCD.obtenerTablasExcelPorPagina(paginas.getInt("idu_conciliacion_excel_pagina"));
				int filaInicioTablaAnterior = cantidadFilas;
				while (tablas.next()) {
					boolean adjuntarTablaAnterior = tablas.getBoolean("opc_adjuntar_anterior");
					boolean mostrarIdentificadorTotal = tablas.getBoolean("opc_mostrar_identificador_total");
					int columnaInicioActual = 0;

					// Obtenemos la configuracion de columnas para la tabla de la página actual
					CachedRowSet columnas = GeneracionArchivosCD.obtenerColumnasExcelPorTabla(tablas.getInt("idu_conciliacion_excel_tabla"));
					if (columnas == null) {
						bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
						bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_OBTENER_COLUMNAS_EXCEL;
						return null;
					}

					// Si no hay columnas para la tabla actual, se registra como error y termina el
					// proceso
					if (!columnas.next()) {
						bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
						bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_EXCEL_TABLA_SIN_COLUMNAS;
						return null;
					}
					columnas.beforeFirst();

					// Lógica para asignar si la tabla empezará debajo o enseguida de la última
					// tabla agregada
					if (!adjuntarTablaAnterior) {
						// Si hay más de una tabla en vertical, se agrega un renglon de separación
						if (!tablas.isFirst()) {
							cantidadFilas++;
						}
						// Como la tabla actual no se va a adjuntar a la anterior, se asigna el inicio
						// de la tabla a la variable de inicio anterior
						// para que la proxima tabla tome este valor de necesitar adjuntarse
						filaInicioTablaAnterior = cantidadFilas;
						columnaActual = 0;
					} else {
						// La fila inicio sería igual a la fila del titulo
						// Se le agrega uno para que tome el renglon siguiente al titulo
						// ya que al estar mergeado el titulo, este indica que su ultima columna es 2
						// en lugar de, por ejemplo, 8.
						// El ultimo +1 es para agregar una columna de separacion entre tablas
						columnaInicioActual = Math
								.addExact(pagina.getRow(Math.addExact(filaInicioTablaAnterior, 1)).getLastCellNum(), 1);
						columnaActual = Math
								.addExact(pagina.getRow(Math.addExact(filaInicioTablaAnterior, 1)).getLastCellNum(), 1);
					}

					int filaActual = filaInicioTablaAnterior;

					/***************************************
					 * GENERACION TITULO TABLA
					 **********************************************/
					// Si la tabla tiene titulo, se agrega
					String valorTitulo = tablas.getString("des_titulo");
					if (!valorTitulo.equals("")) {
						XSSFRow filaTitulo = pagina.getRow(filaActual) == null ? pagina.createRow(filaActual)
								: pagina.getRow(filaActual);
						XSSFCell celdaTitulo = filaTitulo.createCell(columnaInicioActual);
						celdaTitulo.setCellValue(valorTitulo);

						columnas.last();
						int longitudTabla = columnas.getRow();
						pagina.addMergedRegion(new CellRangeAddress(filaActual, filaActual, columnaInicioActual,
								columnaInicioActual + longitudTabla - 1));
						if ((columnaInicioActual + longitudTabla - 1) > columnaMaxima) {
							columnaMaxima = columnaInicioActual + longitudTabla - 1;
						}

						// Se agregan los estilos asignados al titulo
						int idEstiloActualTitulo = tablas.getInt("idu_conciliacion_excel_estilo_titulo");
						XSSFCellStyle estiloActualTitulo;
						// Si se repite el estilo de la iteracion anterior lo utiliza
						if (idEstiloActualTitulo == idEstiloAnterior) {
							estiloActualTitulo = estiloAnterior;
						} else {
							estiloActualTitulo = crearEstiloExcel(excel, idEstiloActualTitulo);
							idEstiloAnterior = idEstiloActualTitulo;
							estiloAnterior = estiloActualTitulo;
						}

						celdaTitulo.setCellStyle(estiloActualTitulo);

						columnas.beforeFirst();

						filaActual++;
					}

					/***************************************
					 * GENERADO CABECEROS TABLAS
					 **********************************************/
					// Generamos/obtenemos la fila para los cabeceros de la tabla
					XSSFRow filaCabeceros = pagina.getRow(filaActual) == null ? pagina.createRow(filaActual)
							: pagina.getRow(filaActual);
					filaActual++;
					// Se agregan los cabeceros de la tabla al archivo
					while (columnas.next()) {
						XSSFCell celda = filaCabeceros.createCell(columnaInicioActual + columnas.getRow() - 1);
						celda.setCellValue(columnas.getString("des_titulo"));

						// logica estilos
						int idEstiloActualCabecero = tablas.getInt("idu_conciliacion_excel_estilo_cabecero");
						XSSFCellStyle estiloActualCabecero;
						if (idEstiloActualCabecero == idEstiloAnterior) {
							estiloActualCabecero = estiloAnterior;
						} else {
							estiloActualCabecero = crearEstiloExcel(excel, idEstiloActualCabecero);
							idEstiloAnterior = idEstiloActualCabecero;
							estiloAnterior = estiloActualCabecero;
						}
						celda.setCellStyle(estiloActualCabecero);
					}

					/***************************************
					 * LLENADO DE TABLAS
					 **********************************************/
					// Se obtiene la informacion del listado de datos, bajo esta lógica solo se
					// admite una tabla por página
					int idFuncionTabla = tablas.getInt("idu_conciliacion_paso");
					CachedRowSet informacionTabla = listaDatos.stream().filter(item -> {
						return item.idPaso == idFuncionTabla;
					}).collect(Collectors.toList()).get(0).rsDatos;
					String identificadorTotal = tablas.getString("des_identificador_total");
					while (informacionTabla.next()) {
						columnaActual = columnaInicioActual;
						boolean esFilaTotal = false;
						XSSFRow filaInformacion = pagina.getRow(filaActual) == null ? pagina.createRow(filaActual)
								: pagina.getRow(filaActual);
						XSSFCellStyle estiloTotal = null;

						columnas.beforeFirst();
						// Se iteran las columnas configuradas para la tabla
						while (columnas.next()) {
							XSSFCell celda = filaInformacion.createCell(columnaActual);
							String valorCelda = informacionTabla
									.getString(columnas.getString("des_identificador_informacion"));
							// Si se identifica el renglon como renglon de totales, se aplica el estilo
							// configurado
							// para estos a todo el renglon
							if (columnas.isFirst() && valorCelda.equals(identificadorTotal)) {
								if(!mostrarIdentificadorTotal) {
									valorCelda = "";
								}
								esFilaTotal = true;
							}

							if (esFilaTotal) {
								if (estiloTotal == null) {
									estiloTotal = crearEstiloExcel(excel,
											tablas.getInt("idu_conciliacion_excel_estilo_total"));
								}

								celda.setCellStyle(estiloTotal);
							} else {
								// logica estilos
								int idEstiloActualColumna = columnas.getInt("idu_conciliacion_excel_estilo");
								XSSFCellStyle estiloActualColumna;
								if (idEstiloActualColumna == idEstiloAnterior) {
									estiloActualColumna = estiloAnterior;
								} else {
									estiloActualColumna = crearEstiloExcel(excel, idEstiloActualColumna);
									idEstiloAnterior = idEstiloActualColumna;
									estiloAnterior = estiloActualColumna;
								}
								celda.setCellStyle(estiloActualColumna);
							}

							celda.setCellValue(valorCelda);
							columnaActual++;
							if (columnaActual > columnaMaxima) {
								columnaMaxima = columnaActual;
							}
						}

						filaActual++;
					}

					// Se ajusta el tamaño de todas las columnas generadas
					for (int i = 0; i < columnaMaxima; i++) {
						pagina.autoSizeColumn(i);
					}

					cantidadFilas = Math.addExact(pagina.getLastRowNum(), 1);
				}

				/***************************************
				 * GENERACION PIES DE PAGINA
				 **********************************************/
				cantidadFilas = crearAnotacionesExcel(cantidadFilas, excel, pagina,
						paginas.getInt("idu_conciliacion_excel_pagina"), false, bitacora);
				if (cantidadFilas < 0) {
					if (cantidadFilas == -1) {
						bitacora.interfaz.mensajeError = generalConstantes.MENSAJE_ERROR_OBTENER_PIES_PAGINA_EXCEL;
					}

					return null;
				}

			}

			// Almacenamos el libro de Excel via ese flujo de datos
			excel.write(new FileOutputStream(archivo));
			Files.setPosixFilePermissions(rutaSeguraArchivo, PosixFilePermissions.fromString("rwxr--r--"));
			rutaAbsolutaArchivo = archivo.getAbsolutePath();
		} catch (Exception e) {
			rutaAbsolutaArchivo = null;
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = generalConstantes.mensajeErrorGenerarExcel(e.toString());
		} finally {
			// Cerramos el libro para concluir operaciones
			if (excel != null) {
				excel.close();
			}
		}

		return rutaAbsolutaArchivo;
	}

	// Genera las secciones de encabezado o pie de pagina según el flag esCabecero
	// para el excel especificado
	private static int crearAnotacionesExcel(int cantidadFilas, XSSFWorkbook excel, XSSFSheet pagina, int idPagina,
			boolean esCabecero, BitacoraCLN bitacora) throws SQLException, IOException, ArithmeticException {
		CachedRowSet anotaciones = GeneracionArchivosCD.obtenerAnotacionesExcelPorPagina(idPagina, esCabecero, bitacora.interfaz.iduBitacora);
		if (anotaciones == null) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			return -1;
		}

		XSSFRow filaAnotacionAnterior = null;
		int idEstiloAnterior = 0;
		XSSFCellStyle estiloAnterior = null;
		int columnaActual = 0;

		while (anotaciones.next()) {
			boolean adjuntarRenglonAnterior = anotaciones.getBoolean("opc_adjuntar_anterior");
			XSSFRow fila = null;
			XSSFCell celda = null;
			if (!adjuntarRenglonAnterior) {
				columnaActual = 0;
				fila = pagina.createRow(cantidadFilas);
			} else {
				cantidadFilas--;
				fila = filaAnotacionAnterior;
			}

			celda = fila.createCell(columnaActual);

			// Se agregan los estilos asignados a la anotacion
			int idEstiloActualAnotacion = anotaciones.getInt("idu_conciliacion_excel_estilo");
			XSSFCellStyle estiloActualAnotacion;
			// Si se repite el estilo de la iteracion anterior lo utiliza
			if (idEstiloActualAnotacion == idEstiloAnterior) {
				estiloActualAnotacion = estiloAnterior;
			} else {
				estiloActualAnotacion = crearEstiloExcel(excel, idEstiloActualAnotacion);
				idEstiloAnterior = idEstiloActualAnotacion;
				estiloAnterior = estiloActualAnotacion;
			}

			celda.setCellStyle(estiloActualAnotacion);

			String valorAnotacion = "";
			try {
				valorAnotacion = reemplazarVariablesAnotacion(anotaciones.getString("des_contenido"));
			} catch (Exception e) {
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = generalConstantes.mensajeErrorObtenerAnotacionesExcel(e.getMessage());
				return -2;
			}

			celda.setCellValue(valorAnotacion);
			int longitudCelda = anotaciones.getInt("num_longitud");
			if (longitudCelda > 1) {
				pagina.addMergedRegion(new CellRangeAddress(cantidadFilas, cantidadFilas, columnaActual,
						columnaActual + longitudCelda - 1));
			}
			columnaActual = Math.addExact(columnaActual, longitudCelda);
			cantidadFilas++;
			filaAnotacionAnterior = fila;
		}

		return cantidadFilas;
	}

	// Reemplaza las variables de fehca y query detectadas dentro de la cadena de
	// anotacion proporcionada por sus valores correspondientes
	private static String reemplazarVariablesAnotacion(String valorAnotacion) throws IOException, SQLException {
		// Si la anotacion cuenta con un parametro de fecha, se reemplaza por la fecha
		// correspondiente
		if (valorAnotacion.contains("@fecha(")) {
			valorAnotacion = reemplazarPatronFecha("@fecha\\(.*?\\)", valorAnotacion, 7, 0);
		}

		if (valorAnotacion.contains("@fechaInicio(")) {
			valorAnotacion = reemplazarPatronFecha("@fechaInicio\\(.*?\\)", valorAnotacion, 13, 7);
		}

		if (valorAnotacion.contains("@fechaFin(")) {
			valorAnotacion = reemplazarPatronFecha("@fechaFin\\(.*?\\)", valorAnotacion, 10, 1);
		}

		// Si la anotacion cuenta con un parametro query, se ejecuta el query y se
		// reemplaza el parametro por el valor retornado por el query
		if (valorAnotacion.contains("@psqlQuery=")) {
			Pattern pattern = Pattern.compile("@psqlQuery=.*?;");
			Matcher matcher = pattern.matcher(valorAnotacion);
			while (matcher.find()) {
				String variableQuery = matcher.group();
				String query = variableQuery.substring(11, variableQuery.length());
				CachedRowSet resultadoQuery = ConsultasCD.consultaGeneral(query);
				String valorResultadoQuery = "";
				if (resultadoQuery.next()) {
					valorResultadoQuery = resultadoQuery.getString(1);
				}

				valorAnotacion = valorAnotacion.substring(0, matcher.start()) + valorResultadoQuery
						+ valorAnotacion.substring(matcher.end(), valorAnotacion.length());
			}
		}

		return valorAnotacion;
	}

	// Metodo encargado del reemplazo de cualquier fecha dentro
	private static String reemplazarPatronFecha(String patron, String valorAnotacion, int longitudVariable,
			int diasRestar) {
		@SuppressWarnings("deprecation")
		Locale local = new Locale("es", "MX");
		Pattern pattern = Pattern.compile(patron);
		Matcher matcher = pattern.matcher(valorAnotacion);
		while (matcher.find()) {
			String variableFecha = matcher.group();
			String formato = variableFecha.substring(longitudVariable, variableFecha.length() - 1);
			DateTimeFormatter formateador = DateTimeFormatter.ofPattern(formato, local);
			String fecha = formateador.format(LocalDate.now().minusDays(diasRestar));
			valorAnotacion = valorAnotacion.substring(0, matcher.start()) + fecha
					+ valorAnotacion.substring(matcher.end(), valorAnotacion.length());
		}

		return valorAnotacion;
	}

	// Genera los estilos de excel desde base de datos para el id de estilo
	// especificado
	private static XSSFCellStyle crearEstiloExcel(XSSFWorkbook libro, int idEstilo) throws SQLException, IOException {
		XSSFCellStyle estilo = libro.createCellStyle();
		XSSFFont fuente = libro.createFont();
		CachedRowSet estiloBd = GeneracionArchivosCD.obtenerDetalleEstiloPorEstiloId(idEstilo);

		while (estiloBd.next()) {
			String claveEstilo = estiloBd.getString("nom_clave_estilo");
			String valorEstilo = estiloBd.getString("des_valor_estilo");

			switch (claveEstilo) {
			case estilosExcelConstantes.ESTILO_EXCEL_FUENTE_COLOR:
				XSSFColor colorFuente = crearColorDeStringRGB(valorEstilo);
				fuente.setColor(colorFuente);
				break;

			case estilosExcelConstantes.ESTILO_EXCEL_FUENTE_NEGRITA:
				if (valorEstilo.equals("1")) {
					fuente.setBold(true);
				}
				break;

			case estilosExcelConstantes.ESTILO_EXCEL_FUENTE_ITALICA:
				if (valorEstilo.equals("1")) {
					fuente.setItalic(true);
				}
				break;

			case estilosExcelConstantes.ESTILO_EXCEL_BORDE_TIPO:
				BorderStyle tipoBorde = BorderStyle.valueOf(valorEstilo);
				estilo.setBorderLeft(tipoBorde);
				estilo.setBorderTop(tipoBorde);
				estilo.setBorderRight(tipoBorde);
				estilo.setBorderBottom(tipoBorde);
				break;

			case estilosExcelConstantes.ESTILO_EXCEL_BORDE_COLOR:
				XSSFColor colorBorde = crearColorDeStringRGB(valorEstilo);
				estilo.setLeftBorderColor(colorBorde);
				estilo.setTopBorderColor(colorBorde);
				estilo.setRightBorderColor(colorBorde);
				estilo.setBottomBorderColor(colorBorde);
				break;

			case estilosExcelConstantes.ESTILO_EXCEL_COLOR_FONDO:
				XSSFColor colorFondo = crearColorDeStringRGB(valorEstilo);
				estilo.setFillForegroundColor(colorFondo);
				estilo.setFillPattern(FillPatternType.SOLID_FOREGROUND);
				break;

			case estilosExcelConstantes.ESTILO_EXCEL_ALINEACION_HORIZONTAL:

				estilo.setAlignment(HorizontalAlignment.valueOf(valorEstilo));
				break;
			}
		}
		estilo.setFont(fuente);
		return estilo;
	}

	// Genera un color XSSFColor a partir de un string con valores RGB separados por
	// comas
	// Ejemplo de string: "255,255,255"
	private static XSSFColor crearColorDeStringRGB(String cadenaRGB) {
		XSSFColor color = null;
		String[] codigosRGB = cadenaRGB.split(",");
		if (codigosRGB.length == 3) {
			byte[] bytesRGB = new byte[] { (byte) Integer.parseInt(codigosRGB[0]),
					(byte) Integer.parseInt(codigosRGB[1]), (byte) Integer.parseInt(codigosRGB[2]) };

			color = new XSSFColor(bytesRGB);
		}

		return color;
	}

	// Genera un archivo tipo pdf en la ruta especificada segun la configuracion en
	// bd de la conciliacion actual
	// se debe pasar un ResultSet en la lista en el caso de necesitar generar una tabla en el pdf.
	public static String generarPDF(int idConciliacion, String sConsulta, String rutaDoc, String nombreDoc,
			BitacoraCLN bitacora) throws IOException, SQLException, DocumentException {

		String rutaPdf = generalConstantes.RUTA_RAIZ_ARCHIVOS_GENERADOS + rutaDoc;
		String rutaAbsolutaArchivo = null;
		try {

			// Si la ruta no existe, la crea
			Utilidades.crearDirectorio(rutaPdf);

			// Crear un nuevo documento PDF
			Document document = new Document();
			Path rutaSeguraArchivo = Paths.get(generalConstantes.RUTA_RAIZ_ARCHIVOS_GENERADOS, rutaDoc, nombreDoc);
			File archivo = new File(rutaSeguraArchivo.toString());
			archivo.setExecutable(false);
			archivo.setReadable(true);
			archivo.setWritable(true);
			// Si la ruta del archivo intenta salir de su raiz designada, detiene el proceso
			// y lo marca como error en la bitacora
			if (!archivo.getCanonicalPath().startsWith(raizDesignada)) {
				throw new IOException(generalConstantes.MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ);
			}
			FileOutputStream pdfGenerado = new FileOutputStream(rutaSeguraArchivo.toString());

			PdfWriter.getInstance(document, pdfGenerado);

			try {

				//consulta para obtener el cuerpo del pdf
				CachedRowSet detallePdfConciliacion = GeneracionArchivosCD.obtenerDetallePdfPorConciliacion(idConciliacion);
				document.open();
				if (detallePdfConciliacion.next()) {

					detallePdfConciliacion.beforeFirst();
					Paragraph titulo = new Paragraph();
					while (detallePdfConciliacion.next()) {
						//se crean las variables necesarias para el formateo del pdf
						String contenido = "";
						boolean negritas = false, saltoLinea = false;
						Integer alineacionTexto = 0, tamanioTablaPdf = 0, recorrido1 = 0;
						contenido = detallePdfConciliacion.getString("des_contenido");
						negritas = detallePdfConciliacion.getBoolean("opc_negritas");
						saltoLinea = detallePdfConciliacion.getBoolean("opc_salto_linea");
						alineacionTexto = detallePdfConciliacion.getInt("num_alineacion");

						if (contenido.indexOf("@TABLAPDF") == 0) {
							// ejecuta la consulta necesaria en caso de necesitar crear una tabla con informacion de bd dinamicamente
							CachedRowSet tablaPdfConciliacion = GeneracionArchivosCD.obtenerTablasPdfPorConciliacion(sConsulta,
									idConciliacion);
							if (tablaPdfConciliacion.next()) {
								tablaPdfConciliacion.last();

								tamanioTablaPdf = tablaPdfConciliacion.getMetaData().getColumnCount();

								tablaPdfConciliacion.beforeFirst();
								PdfPTable tablaPdf = new PdfPTable(tamanioTablaPdf);
					            Font boldFont = new Font(Font.FontFamily.TIMES_ROMAN, 10, Font.BOLD);
					            Font boldFontSinN = new Font(Font.FontFamily.TIMES_ROMAN, 10);
					            PdfPCell headerCell;
					            PdfPCell bodyCell;

								while (tablaPdfConciliacion.next()) {
									if (recorrido1 == 0) {
										//se crea las columnas y las filas de la tabla de manera dinamica
										for (int i = 1; i <= tamanioTablaPdf; i++) {
							                headerCell = new PdfPCell(new Phrase(tablaPdfConciliacion.getMetaData().getColumnName(i), boldFont));
											tablaPdf.addCell(headerCell);
										}
									}
									for (int j = 1; j <= tamanioTablaPdf; j++) {
						                bodyCell = new PdfPCell(new Phrase(tablaPdfConciliacion.getString(j), boldFontSinN));
										tablaPdf.addCell(bodyCell);
									}
									recorrido1++;
								}
								document.add(new Paragraph("\n"));
								document.add(tablaPdf);
								document.add(new Paragraph("\n"));
							}
						} else {
							//se dan los estilos a los parrafos a agregar al pdf
							if (negritas) {
								Font font1 = new Font(Font.FontFamily.UNDEFINED, 12, Font.BOLD);
								titulo.setFont(font1);
							}
							else {
								Font font1 = new Font(Font.FontFamily.UNDEFINED, 12, Font.UNDEFINED);
								titulo.setFont(font1);
							}
							if (alineacionTexto == 0) {
								titulo.setAlignment(Element.ALIGN_UNDEFINED);
							} else if (alineacionTexto == 1) {
								titulo.setAlignment(Element.ALIGN_LEFT);
							} else if (alineacionTexto == 2) {
								titulo.setAlignment(Element.ALIGN_CENTER);
							} else {
								titulo.setAlignment(Element.ALIGN_RIGHT);
							}
							titulo.add(contenido);
							if (saltoLinea) {
								document.add(titulo);
								titulo.clear();
							}
						}
					}
					document.add(titulo);
					rutaAbsolutaArchivo = rutaSeguraArchivo.toString();
				}
				// se agrega codigo para correccion de vulnerabilidad de checkmark aunque no sea
				// necesario
				if (document.isOpen()) {
					document.close();
				}
			} catch (DocumentException e) {
				//absorcion de errores
				rutaAbsolutaArchivo = null;
				bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
				bitacora.interfaz.mensajeError = generalConstantes.mensajeErrorGenerarPdf(e.toString());
			}

			finally {
				// Cerrar el documento
				if (pdfGenerado != null) {
					pdfGenerado.close();
				}
				if (document.isOpen()) {
					document.close();
				}
			}
		}catch(Exception ex) {
			//absorcion de errores
			rutaAbsolutaArchivo = null;
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = generalConstantes.mensajeErrorGenerarPdf(ex.toString());
		}
		finally {

		}

		return rutaAbsolutaArchivo;
	}
}
