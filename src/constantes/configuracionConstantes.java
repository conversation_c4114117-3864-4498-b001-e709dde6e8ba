package constantes;

public final class configuracionConstantes {
	// Se oculta el constructor para evitar el instanciamiento de esta clase
	private configuracionConstantes() {}
	// Correo
	public static final String CORREO_REMITENTE = "CORREO_REMITENTE";
	public static final String CORREO_REMITENTE_CLAVE = "CORREO_REMITENTE_CLAVE";
	public static final String CORREO_REMITENTE_APODO = "CORREO_REMITENTE_APODO";
	public static final String CORREO_PROTOCOLO = "CORREO_PROTOCOLO";
	public static final String CORREO_HOST = "CORREO_HOST";
	public static final String CORREO_PUERTO = "CORREO_PUERTO";
	public static final String CORREO_STARTTLS_ENABLE = "CORREO_STARTTLS_ENABLE";
	public static final String CORREO_AUTH = "CORREO_AUTH";
	public static final String SERVIDOR_CORREO = "SERVIDOR_CORREO";
	public static final String PUERTO_CORREO = "PUERTO_CORREO";
	public static final String PATH_API_TOKEN_CORREO = "PATH_API_TOKEN_CORREO";
	public static final String PATH_API_CORREO = "PATH_API_CORREO";
	public static final String USUARIO_API_CORREO = "USUARIO_API_CORREO";
	public static final String CONTRASENA_API_CORREO = "CONTRASENA_API_CORREO";

}