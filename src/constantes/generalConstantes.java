package constantes;

import java.nio.file.Paths;

public final class generalConstantes {
	// Se oculta el constructor para evitar el instanciamiento de esta clase
	private generalConstantes() {}

	// Main
	public static final String MENSAJE_CANTIDAD_PARAMETROS_ERRONEA = "La cantidad de parametros proporcionados al jar es incorrecta.";
	public static final String MENSAJE_SIN_CONEXION_A_BD = "No se pudo conectar a la base de datos.";
	public static final String MENSAJE_CONCILIACION_NO_VALIDA = "El proceso no existe o esta bloqueado";
	public static final String AUTHORIZATION = "AUTHORIZATION";

	// Mensajes de bitacora
	public static final String MENSAJE_PROCESO_EXITOSO = "Termino el proceso exitosamente.";
	public static final String MENSAJE_PROCESO_EXITOSO_SIN_ESTADISTICA = "Termino el proceso exitosamente, pero no se pudo actualizar las estadisticas en bitacora";
	public static final String MENSAJE_PASO_EXITOSO_SIN_RESULTADOS = "Ejecucion realizada correctamente, pero no retorno resultados.";
	public static final String MENSAJE_ERROR_INICIAR_BITACORA = "Hubo un error al iniciar la bitacora, error: ";
	public static final String MENSAJE_ERROR_ACTUALIZAR_BITACORA = "Hubo un error al actualizar la bitacora, error: ";
	public static final String MENSAJE_ERROR_ACTUALIZAR_BITACORA_DETALLE = "Hubo un error al actualizar el detalle de la bitacora, error: ";
	public static final String MENSAJE_ERROR_PROCESO = "Ocurrio un error en el proceso.";
	public static final String MENSAJE_ERROR_FALTA_LOGICA = "El proceso no cuenta con etapas/pasos registrados en base de datos.";
	public static final String MENSAJE_ERROR_VALIDACION_PASO_SIN_RESULTADOS = "El script de validacion no retornó resultados.";
	public static final String mensajeErrorValidarPaso (String excepcion) { return "Ocurrio un error al validar el paso: " + excepcion; }

	// Generacion de archivos
	public static final String RUTA_RAIZ_ARCHIVOS_GENERADOS = "descargas/";
	public static final String MENSAJE_ERROR_GENERAR_ARCHIVO_TEXTO = "Ocurrio un error al generar el archivo de texto.";
	public static final String MENSAJE_ERROR_GENERAR_ARCHIVO_EXCEL = "Ocurrio un error al generar el archivo de excel.";
	public static final String MENSAJE_ERROR_GENERAR_RUTA = "Ocurrio un error al generar la ruta: ";
	public static final String MENSAJE_ERROR_GENERAR_ARCHIVO_PDF = "Ocurrio un error al generar el archivo pdf.";
	public static final String MENSAJE_ERROR_OBTENER_PAGINAS_EXCEL = "Ocurrio un error al consultar las paginas del excel.";
	public static final String MENSAJE_EXCEL_SIN_PAGINAS = "La conciliacion no cuenta con paginas configuradas para su generacion de excel.";
	public static final String MENSAJE_EXCEL_TABLA_SIN_COLUMNAS = "La conciliacion no cuenta con columnas configuradas para una de sus tablas para el generado de excel.";
	public static final String MENSAJE_ERROR_OBTENER_ENCABEZADOS_EXCEL = "Ocurrio un error al consultar los encabezados para el excel.";
	public static final String MENSAJE_ERROR_OBTENER_PIES_PAGINA_EXCEL = "Ocurrio un error al consultar los pies de pagina para el excel.";
	public static final String MENSAJE_ERROR_OBTENER_COLUMNAS_EXCEL = "Ocurrio un error al consultar las columnas del excel.";
	public static final String MENSAJE_EXCEL_SIN_INFORMACION = "No hay informacion disponible para llenar el archivo de excel.";
	public static final String MENSAJE_TXT_SIN_INFORMACION = "No hay informacion disponible para llenar el archivo de texto.";
	public static final String MENSAJE_EXCEPCION_CREACION_ARCHIVO_FUERA_RAIZ = "No se pueden crear archivos fuera de su raiz designada.";
	public static final String mensajeErrorGenerarExcel (String excepcion) { return "Ocurrio un error al generar el archivo de excel: " + excepcion; }
	public static final String mensajeErrorGenerarPdf (String excepcion) { return "Ocurrio un error al generar el archivo de pdf: " + excepcion; }
	public static final String mensajeErrorObtenerPaginasExcel (String excepcion) { return "Ocurrido un error al obtener las paginas del excel: " + excepcion; }
	public static final String mensajeErrorObtenerAnotacionesExcel (String excepcion) { return "Ocurrido un error al obtener las anotaciones del excel: " + excepcion; }
	public static final String mensajeErrorObtenerTablasExcel (String excepcion) { return "Ocurrio un error al obtener las tablas del excel: " + excepcion; }
	public static final String mensajeErrorObtenerColumnasExcel (String excepcion) { return "Ocurrio un error al obtener las columnas de la tabla de excel: " + excepcion; }
	public static final String mensajeErrorObtenerDetallesEstiloExcel (String excepcion) { return "Ocurrio un error al obtener el detalle de un estilo para excel: " + excepcion; }
	public static final String MENSAJE_ERROR_ARCHIVO_NO_ENCONTRADO = "El archivo no se encuentra en la ruta especificada.";

	// Mensajes envio de correos
	public static final String MENSAJE_ERROR_CORREO_SIN_CORREOS = "No hay correos a enviar configurados.";
	public static final String MENSAJE_ERROR_CORREO_SIN_DESTINATARIOS = "No hay destinatarios configurados para los correos.";
	public static final String MENSAJE_ERROR_CORREO_ALERTA_SIN_DESTINATARIOS = "No hay destinatarios configurados para el correo de soporte.";
	public static final String MENSAJE_ERROR_CORREO_ADJUNTO_SIN_DESTINATARIOS = "No hay destinatarios configurados para el correo del usuario.";
	public static final String MENSAJE_ERROR_CORREO_ARCHIVO_INEXISTENTE = "No existe el archivo a enviar.";

	// ConsultasCD
	public static final String mensajeErrorConsultaGeneral (String consulta, String excepcion) { return "Ha ocurrido un error al ejecutar la consulta " + consulta + ", error: " + excepcion; }
	public static final String mensajeErrorObtenerConfiguracionCorreo (String excepcion) { return "Ocurrio un error al consultar las configuraciones de envio de correos: " + excepcion; }
	public static final String mensajeErrorObtenerCorreos (String excepcion) { return "Ocurrio un error al consultar los correos a enviar: " + excepcion; }
	public static final String mensajeErrorObtenerCorreoDestinatarios (String excepcion) { return "Ocurrio un error al consultar los destinatarios de los correos a enviar: " + excepcion; }

	// ConexionCD
	public static final String mensajeErrorConexion (String excepcion) { return "Ocurrio un error al conectarse a la base de datos: " + excepcion; }
	public static final String mensajeErrorValidarConexion (String excepcion) { return "Ocurrio un error al validar la conexion a base de datos: " + excepcion; }
	public static final String MENSAJE_ERROR_DATOS_CONEXION = "No se encontraron datos de conexión para el id proporcionado";
	public static final String mensajeErrorAPICredenciales (String codigo, String mensaje) { return "Ocurrio un error al obtener los datos de conexion a la BD: [" + codigo + "]" + mensaje; }
	public static final String mensajeErrorAPIToken (String codigo, String mensaje) { return "Ocurrio un error al obtener el token de la api de envio de correos: [" + codigo + "]" +"token api => " + mensaje; }
	public static final String MENSAJE_API_TOKEN_NO_EXISTE = "Token no generado. La aplicacion configurada no tiene autorizacion.";
	public static final String mensajeAPICredencialesNoExisten (String categoria) { return "No existen credenciales de acceso para la categoria: " + categoria; }
	public static final String MENSAJE_API_TOKEN_NO_GENERADO = "Token no generado. Hubo un error al generar el token.";
	public static final String MENSAJE_API_CORREO_NO_ENVIADO = "Hubo un error al enviar el correo. Error ";

	// Proceso
	public static final String mensajeErrorEjecutarProceso(int idProceso, String excepcion) { return "Ocurrio un error al ejecutar el proceso " + idProceso + ", error: " + excepcion; }
	public static final String mensajeErrorValidarProceso(int idProceso, String excepcion) { return "Ocurrio un error al validar el proceso " + idProceso + ", error: " + excepcion; }
	public static final String mensajeErrorObtenerLogicaProceso(int idProceso, String excepcion) { return "Ocurrio un error al obtener la informacion del proceso " + idProceso + ", error: " + excepcion; }

	// Log
	public static final String mensajeErrorCrearRutaLog (String excepcion) { return "Ocurrio un error al crear el directorio del log: " + excepcion; }
	public static final String mensajeErrorGrabarLog (String excepcion) { return "Ocurrio un error al grabar en el log: " + excepcion; }

	//Mensajes consumo servicio SOAP
	public static final String mensajeErrorServicio (String codigo, String mensaje) { return "Error: " + codigo + " - " + mensaje; }
	public static final String MENSAJE_ERROR_CONSUMO = "Ocurrio un error en el consumo de servicio soap, error: ";
	public static final String MENSAJE_ERROR_CODIGO_SOLICITUD = "La solicitud SOAP fallo con el codigo de estado: ";
	public static final String MENSAJE_ERROR_CODIGO_RESPUESTA = "La respuesta fue diferente a lo esperado, validar el servicio.";

	//Mensajes consumo API REST
	public static final String MENSAJE_ERROR_API = "Ocurrio un error en el consumo de servicio Api Rest, error: ";
	public static final String mensajeErrorAPIREST(String codigo, String mensaje) { return "Ocurrio un error al conectarse a la API: [" + codigo + "]" + mensaje; }

	//Palabra de encriptacion y desencriptacion
	public static final String PALABRA_METODO_ENCRIPTACION = "G4m4n_S0lut10n5";
	public static final String METODO_ENCRIPTACION = "PBEWithHMACSHA512AndAES_256";

	//Rutas archivos
	public static final String RUTA_ARCHIVO_PROPERTIES = "./api.properties";

	//Constantes Librerias
	public static final String LIBRERIA_DISALLOW_DOCTYPE_DEC = "http://apache.org/xml/features/disallow-doctype-decl";
	public static final String LIBRERIA_EXTERNAL_GENERAL_ENTITIES = "http://xml.org/sax/features/external-general-entities";
	public static final String LIBRERIA_EXTERNAL_PARAMETER_ENTITIES = "http://xml.org/sax/features/external-parameter-entities";
	public static final String LIBRERIA_LOAD_EXTERNAL_DTD = "http://apache.org/xml/features/nonvalidating/load-external-dtd";

	//Vulnerabilidades
	public static final String EXCEPCION_MAXIMO_CARACTERES = "Se excedio el numero de caracteres permitido.";
	public static final String RUTA_RAIZ = Paths.get(System.getProperty("user.dir")).toString();

	//Codigos de estatus HTTP
	public static final Integer CODIGO_ESTATUS_200 = 200;
	public static final Integer CODIGO_ESTATUS_204 = 204;
	public static final Integer CODIGO_ESTATUS_400 = 400;
	public static final Integer CODIGO_ESTATUS_404 = 404;
	public static final Integer CODIGO_ESTATUS_500 = 500;

}
