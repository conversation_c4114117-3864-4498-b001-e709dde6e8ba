package constantes;

public final class tiposPasosConstantes {
	// Se oculta el constructor para evitar el instanciamiento de esta clase
	private tiposPasosConstantes() {}

	public static final int TIPO_EJECUTAR_SCRIPT = 1;
	public static final int TIPO_GENERAR_EXCEL = 2;
	public static final int TIPO_GENERAR_TXT = 3;
	public static final int TIPO_ENVIAR_CORREO = 4;
	public static final int TIPO_SUBIR_ARCHIVO = 5;
	public static final int TIPO_BAJAR_ARCHIVO = 6;
	public static final int TIPO_CONSUMO_SERVICIO_SOAP = 7;
	public static final int TIPO_WEB_SERVICE_CONCILIACIONES = 8;
	public static final int TIPO_CONVERTIR_XLSX_EN_CSV = 9;
	public static final int TIPO_GENERAR_PDF = 10;
	public static final int TIPO_EJECUTAR_SH = 11;
	public static final int TIPO_COPIAR_ARCHIVO = 12;
	public static final int TIPO_BAJAR_ARCHIVO_SFTP = 13;
	public static final int TIPO_CONSUMO_API_REST = 14;
	public static final int TIPO_EJECUTAR_SCRIPT_CON_XML = 15;
}
