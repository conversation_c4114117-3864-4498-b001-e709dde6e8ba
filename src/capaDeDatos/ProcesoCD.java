package capaDeDatos;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.rowset.CachedRowSet;
import javax.sql.rowset.RowSetFactory;
import javax.sql.rowset.RowSetProvider;

import capaDeLogicaDeNegocios.BitacoraCLN;
import capaDeLogicaDeNegocios.LogCLN;
import constantes.estatusConciliacionConstantes;
import constantes.generalConstantes;
import utilidades.Utilidades;

public class ProcesoCD extends Thread {

	private static ConexionCD conexion = new ConexionCD();
	private static Connection ingresosBD = null;

	static LogCLN log = new LogCLN();

	// Metodo para validar en base de datos si el proceso es valido para la ejecucion
	public static boolean validarProceso(int iduProceso) throws IOException, SQLException {
		boolean valido = false;

		try {
			ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
			PreparedStatement stmt = ingresosBD
					.prepareStatement("SELECT respuesta FROM fun_validar_conciliacion(?::INTEGER);");
			stmt.setInt(1, iduProceso);
			ResultSet rs = stmt.executeQuery();

			if (rs.next()) {
				if (rs.getInt(1) == 1) {
					valido = true;
				} else {
					log.grabarLog(generalConstantes.MENSAJE_CONCILIACION_NO_VALIDA);
				}
			}
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorValidarProceso(iduProceso, e.getMessage()));
		} finally {
			ingresosBD.close();
		}

		return valido;
	}

	// Metodo para obtener los pasos de la logica del proceso en ejecucion desde base de datos
	public static CachedRowSet obtenerLogicaProceso(int iduProceso,int idBitacora) throws IOException, SQLException {
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet respuesta = factory.createCachedRowSet();
		try {
			ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

			PreparedStatement stmt = ingresosBD.prepareStatement("SELECT idetapa, desetapa, idpaso, despaso,"
					+ "idtipo, idorigen, servidor, puerto, ruta, usuario, cntra, nomarchivo, rutadestino, nomarchivodestino,"
					+ "opcenviarporcorreo, extension, sfunsql, clvconexion FROM fun_consultar_logica_proceso(?::INTEGER,?::INTEGER);");
			stmt.setInt(1, iduProceso);
			stmt.setInt(2, idBitacora);
			respuesta.populate(stmt.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerLogicaProceso(iduProceso, e.getMessage()));

		} finally {
			ingresosBD.close();

		}

		return respuesta;
	}

	// Método que ejecuta el script proporcionado en la conexion proporcionada, se encarga de las ejecuciones de script configuradas para los pasos
	public static CachedRowSet ejecutarScriptPaso(String script, int idConexion, BitacoraCLN bitacora) throws IOException, SQLException {
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultado = factory.createCachedRowSet();

		try(
				Connection conexionBD = conexion.realizarConexionPorId(idConexion);
				PreparedStatement consultaPreparada  = conexionBD.prepareStatement(script, ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
				) {
			ResultSet tst = consultaPreparada.executeQuery();
			resultado.populate(tst);
		} catch (Exception e) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = e.toString();
		}

		return resultado;
	}

	// Metodo que obtiene la lista de parametros asignados al paso proporcionado
	public static CachedRowSet obtenerParametros(int iduPaso,int idConexion, BitacoraCLN bitacora) throws IOException, SQLException {
		Connection conexionBD = conexion.realizarConexionPorId(idConexion);
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultado = factory.createCachedRowSet();

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = conexionBD.prepareStatement("SELECT nomparametro, desparametro FROM fun_obtener_parametros_por_paso(?, ?);",
					ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
			consultaPreparada.setInt(1, iduPaso);
			consultaPreparada.setInt(2, bitacora.interfaz.iduBitacora);
			resultado.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = e.toString();
		} finally {
			conexionBD.close();
		}

		return resultado;
	}

	// Metodo que obtiene la lista de parametros para la API REST del paso proporcionado
	public static CachedRowSet obtenerParametrosApiRest(int iduPaso,int idConexion, BitacoraCLN bitacora) throws IOException, SQLException {
		Connection conexionBD = conexion.realizarConexionPorId(idConexion);
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultado = factory.createCachedRowSet();

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = conexionBD.prepareStatement(
					"SELECT nomparametro, desparametro FROM fun_obtener_parametros_api_rest_por_paso(?, ?);",
					ResultSet.TYPE_SCROLL_INSENSITIVE,
					ResultSet.CONCUR_UPDATABLE);
			consultaPreparada.setInt(1, iduPaso);
			consultaPreparada.setInt(2, bitacora.interfaz.iduBitacora);
			resultado.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			bitacora.interfaz.iduEstatus = estatusConciliacionConstantes.ERROR;
			bitacora.interfaz.mensajeError = e.toString();
		} finally {
			conexionBD.close();
		}

		return resultado;
	}
}
