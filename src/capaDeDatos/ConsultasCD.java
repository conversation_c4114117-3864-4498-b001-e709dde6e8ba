package capaDeDatos;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import javax.sql.rowset.CachedRowSet;
import javax.sql.rowset.RowSetFactory;
import javax.sql.rowset.RowSetProvider;

import capaDeLogicaDeNegocios.GeneracionArchivosCLN;
import capaDeLogicaDeNegocios.LogCLN;
import capaDeLogicaDeNegocios.SambaCLN;
import constantes.generalConstantes;
import entidades.BitacoraInterfase;
import utilidades.Utilidades;

public class ConsultasCD {
	static String[] arregloCorreos;
	static String[] arregloArchivosServidorOrigen;
	static String[] arregloArchivosServidor;
	static String[] arregloArchivosServidorPostgresql;
	static String[] parametro;
	static Integer idConciliacion;
	static Integer idConciliacionEstatus;

	static SambaCLN protocoloSamba = new SambaCLN();
	static GeneracionArchivosCLN generacionArchivo = new GeneracionArchivosCLN();

	private static ConexionCD conexion = new ConexionCD();
	private static Connection ingresosBD = null;

	static LogCLN log = new LogCLN();

	public static CachedRowSet consultaGeneral(String script) throws SQLException, IOException {
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultado = factory.createCachedRowSet();

		try {
			ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(script, ResultSet.TYPE_SCROLL_INSENSITIVE,
					ResultSet.CONCUR_UPDATABLE);
			resultado.populate(consultaPreparada.executeQuery());

		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorConsultaGeneral(script, e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultado;
	}

	// Metodo para obtener las configuraciones necesarias para el envío de correos
	public static ResultSet consultarConfiguracionesCorreo() throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		ResultSet resultadoConsulta = null;
		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
					"SELECT idu_configuracion,des_clave,des_valor_texto FROM fun_consultar_conciliacion_configuracion_correo();",
					ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
			// ejecuta la consulta
			resultadoConsulta = consultaPreparada.executeQuery();
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerConfiguracionCorreo(e.getMessage()));
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene la lista de correos a enviar para la conciliacion especificada
	public static ResultSet consultarCorreosEnviar(int idConciliacion, int idPaso, int idBitacora)
			throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		ResultSet resultadoConsulta = null;

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
					"SELECT idu_correo,des_asunto,des_cuerpo,opc_alerta FROM fun_consultar_conciliacion_correos(?,?,?);",
					ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);

			consultaPreparada.setInt(1, idConciliacion);
			consultaPreparada.setInt(2, idPaso);
			consultaPreparada.setInt(3, idBitacora);
			// ejecuta la consulta
			resultadoConsulta = consultaPreparada.executeQuery();
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerCorreos(e.getMessage()));
		} finally {
			ingresosBD.close();
		}
		return resultadoConsulta;
	}

	// Obtiene la lista de destinatarios configurados para el correo especificado
	public static ResultSet consultarCorreosDestinatarios(int idCorreo) throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		ResultSet resultadoConsulta = null;
		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
					"SELECT des_destinatario FROM fun_consultar_conciliacion_correos_destinatarios(?);",
					ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);

			consultaPreparada.setInt(1, idCorreo);
			// ejecuta la consulta
			resultadoConsulta = consultaPreparada.executeQuery();
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerCorreoDestinatarios(e.getMessage()));
		} finally {
			ingresosBD.close();
		}
		return resultadoConsulta;
	}

	// Metodo para obtener el correo general en caso de que el proceso no tenga
	// informacion que procesar
	public static ResultSet consultarCorreoSinArchivo(BitacoraInterfase bitacora) throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		ResultSet resultadoConsulta = null;
		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
					"SELECT des_cuerpo FROM fun_consultar_conciliacion_correo_sin_archivo(?);",
					ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_UPDATABLE);
			consultaPreparada.setInt(1, bitacora.iduBitacora);

			// ejecuta la consulta
			resultadoConsulta = consultaPreparada.executeQuery();
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerConfiguracionCorreo(e.getMessage()));
		} finally {
			ingresosBD.close();
		}
		return resultadoConsulta;
	}
}
