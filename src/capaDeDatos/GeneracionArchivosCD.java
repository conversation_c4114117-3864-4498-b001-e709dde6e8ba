package capaDeDatos;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import javax.sql.rowset.CachedRowSet;
import javax.sql.rowset.RowSetFactory;
import javax.sql.rowset.RowSetProvider;

import capaDeLogicaDeNegocios.LogCLN;
import constantes.generalConstantes;
import utilidades.Utilidades;

public class GeneracionArchivosCD {
	private static ConexionCD conexion = new ConexionCD();
	private static Connection ingresosBD = null;
	private static String textoFecha = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
	static LogCLN log = new LogCLN();

	// Obtiene la lista de paginas que tendrá el excel de la conciliación especificada
	public static CachedRowSet obtenerPaginasExcelPorConciliacion(int idConciliacion) throws IOException, SQLException {
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
				"SELECT idu_conciliacion_excel_pagina, nom_pagina FROM fun_obtener_conciliacion_excel_paginas(?::INT);",
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			consultaPreparada.setInt(1, idConciliacion);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerPaginasExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene la lista de anotaciones que tendrá el excel de la conciliación especificada
	// filtrados por encabezados o pie de pagina
	public static CachedRowSet obtenerAnotacionesExcelPorPagina(int idPagina, boolean esEncabezado, int idBitacora) throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
				"SELECT idu_conciliacion_excel_anotacion,idu_conciliacion_excel_estilo,des_contenido,num_longitud,opc_adjuntar_anterior FROM fun_obtener_conciliacion_excel_anotaciones_por_pagina(?::INT,?::BIT, ?::INT);",
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			consultaPreparada.setInt(1, idPagina);
			consultaPreparada.setInt(2, esEncabezado ? 1 : 0);
			consultaPreparada.setInt(3, idBitacora);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerAnotacionesExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene la lista de tablas que tendrá la pagina de excel especificada
	public static CachedRowSet obtenerTablasExcelPorPagina(int idPagina) throws IOException, SQLException {
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
				"SELECT idu_conciliacion_excel_tabla,idu_conciliacion_excel_estilo_titulo,idu_conciliacion_excel_estilo_cabecero,idu_conciliacion_excel_estilo_total,idu_conciliacion_paso, fun_sql,des_titulo,des_identificador_total,opc_adjuntar_anterior, opc_mostrar_identificador_total FROM fun_obtener_conciliacion_excel_tablas_por_pagina(?::INT);",
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			consultaPreparada.setInt(1, idPagina);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerTablasExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene la lista de columnas que tendrá la tabla de excel de la página especificada
	public static CachedRowSet obtenerColumnasExcelPorTabla(int idTabla) throws IOException, SQLException {
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
				"SELECT idu_conciliacion_excel_tabla_columna,idu_conciliacion_excel_estilo,des_titulo,des_identificador_informacion FROM fun_obtener_conciliacion_excel_columnas_por_tabla(?::INT);",
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			consultaPreparada.setInt(1, idTabla);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerColumnasExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene los detalles del estilo especificado
	public static CachedRowSet obtenerDetalleEstiloPorEstiloId(int idEstilo) throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
				"SELECT nom_clave_estilo, des_valor_estilo FROM fun_obtener_conciliacion_excel_detalles_estilo_por_estilo_id(?::INT);",
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			consultaPreparada.setInt(1, idEstilo);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerDetallesEstiloExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene la lista de tablas que el excel a generar, esto para validar que exista informacion para todas las tablas
	public static CachedRowSet obtenerTablasExcelPorConciliacion(int idConciliacion) throws IOException, SQLException {
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
				"SELECT idu_conciliacion_paso FROM fun_obtener_conciliacion_excel_tablas_por_conciliacion(?::INT);",
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			consultaPreparada.setInt(1, idConciliacion);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerTablasExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene el detalle del pdf a generar, esto para validar que exista el pdf para la conciliacion a realizar
	public static CachedRowSet obtenerDetallePdfPorConciliacion(int idConciliacion) throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
				"SELECT idu_conciliacion,des_contenido,num_orden,opc_salto_linea,opc_negritas,num_alineacion FROM fun_consultar_variables_pdf_conciliacion(?::INT,?::TEXT);",
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			consultaPreparada.setInt(1, idConciliacion);
			consultaPreparada.setString(2, textoFecha);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerTablasExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}

	// Obtiene la lista de tablas que el pdf necesita generar , esto para validar que exista informacion para todas las tablas
	public static CachedRowSet obtenerTablasPdfPorConciliacion(String consulta, int idConciliacion) throws IOException, SQLException {
		ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
		RowSetFactory factory = RowSetProvider.newFactory();
		CachedRowSet resultadoConsulta = factory.createCachedRowSet();

		try {
			PreparedStatement consultaPreparada;
			consultaPreparada = ingresosBD.prepareStatement(
					consulta,
				ResultSet.TYPE_SCROLL_INSENSITIVE,
				ResultSet.CONCUR_UPDATABLE
			);
			resultadoConsulta.populate(consultaPreparada.executeQuery());
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorObtenerTablasExcel(e.getMessage()));
			return null;
		} finally {
			ingresosBD.close();
		}

		return resultadoConsulta;
	}
}
