package capaDeDatos;

import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import com.fasterxml.jackson.databind.ObjectMapper;

import capaDeLogicaDeNegocios.LogCLN;
import constantes.generalConstantes;
import entidades.ConexionInterfase;
import utilidades.Utilidades;

public class ConexionCD {

	static LogCLN log = new LogCLN();
	private Connection conexion;

	// Metodo para obtener de la api las credenciales de conexion a una BD
	public ConexionInterfase obtenerDatosConexionPorCategoria(int idCategoria) throws SQLException, IOException {
		ConexionInterfase credenciales = new ConexionInterfase();
		try(FileInputStream entradaDatos = new FileInputStream(generalConstantes.RUTA_ARCHIVO_PROPERTIES)) {
			Properties config = new Properties();
			if (Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
				config.load(entradaDatos);
			}

			String token = obtenerTokenAutorizacionAPIConexiones();
			if(token != "" && token != null) {

				HttpClient clienteHTTP = HttpClient.newHttpClient();
				Map<String, Object> categorias = new HashMap<>();
				categorias.put("categoria", idCategoria);
				Map<String, Object> cuerpoPeticion = new HashMap<>();
				cuerpoPeticion.put("category", config.getProperty("credenciales.categoria"));
				cuerpoPeticion.put("name", config.getProperty("credenciales.nombre"));
				cuerpoPeticion.put("type", config.getProperty("credenciales.tipo"));
				cuerpoPeticion.put("aplicacion", config.getProperty("credenciales.nombreApp"));
				cuerpoPeticion.put("categorias", categorias);
				cuerpoPeticion.put("rutaValidacion", config.getProperty("token.urlVal"));
				ObjectMapper objectMapper = new ObjectMapper();
			    String cuerpoJSON = objectMapper.writeValueAsString(cuerpoPeticion);
			    HttpResponse<String> peticion = clienteHTTP.send(
					HttpRequest
						.newBuilder(new URI(config.getProperty("base") + ":" + config.getProperty("credenciales.puerto") + config.getProperty("credenciales.url")))
						.header("Authorization", "Bearer " + token)
						.header("Accept", "*/*")
						.header("Content-Type", "application/json")
						.POST(BodyPublishers.ofString(cuerpoJSON))
						.timeout(Duration.ofSeconds(10)) // Tiempo máximo de conexión // Establece un límite de tiempo de 10 segundos
						.build(),
					BodyHandlers.ofString()
				);

				int estatusRespuesta = peticion.statusCode();
				String respuesta = peticion.body();
				ConexionInterfase[] respuestaCredenciales = objectMapper.readValue(respuesta, ConexionInterfase[].class);
				if (estatusRespuesta >= generalConstantes.CODIGO_ESTATUS_400) {
					throw new Exception(generalConstantes.mensajeErrorAPICredenciales(String.valueOf(estatusRespuesta), respuesta));
				} else if (respuestaCredenciales.length <= 0) {
					throw new Exception(generalConstantes.mensajeAPICredencialesNoExisten(String.valueOf(idCategoria)));
				}
				credenciales = respuestaCredenciales[0];
			}
			else {
				throw new Exception(generalConstantes.mensajeErrorAPICredenciales(generalConstantes.CODIGO_ESTATUS_404.toString(), generalConstantes.MENSAJE_API_TOKEN_NO_GENERADO));
			}
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorConexion(e.getMessage()));
		}

		return credenciales;
	}

	// Metodo para obtener una lista de credenciales de conexion a bd
	public ArrayList<ConexionInterfase> obtenerListaDatosConexion(ArrayList<Integer> categorias) throws SQLException, IOException {
		ArrayList<ConexionInterfase> listaConexiones = new ArrayList<>();
		for(int categoria : categorias) {
			listaConexiones.add(obtenerDatosConexionPorCategoria(categoria));
		}

		return listaConexiones;
	}

	// Metodo para realizar conexion a bases de datos mediante id
	public Connection realizarConexionPorId(int idConexion) throws SQLException, IOException {
		try(FileInputStream entradaDatos = new FileInputStream(generalConstantes.RUTA_ARCHIVO_PROPERTIES)) {
			ConexionInterfase credenciales = obtenerDatosConexionPorCategoria(idConexion);

			String cadenaConexion = null;
			switch (credenciales.tipo) {
				case "postgres":
					cadenaConexion = "jdbc:postgresql://" + credenciales.servidor + ":" + credenciales.puerto + "/" + credenciales.db;
					break;
				case "mssql":
					cadenaConexion =
						"jdbc:sqlserver://" + credenciales.servidor + ":" + credenciales.puerto +
						";databaseName=" + credenciales.db + ";encrypt=true;trustServerCertificate=true;";
					break;
				default:
					break;
			}
			this.conexion = DriverManager.getConnection(
				cadenaConexion,
				credenciales.usuario,
				credenciales.clsinfo
			);
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorConexion(e.getMessage()));
		}

		return this.conexion;
	}

	// Metodo para validar que se pueda realizar la conexion a la base de datos
	public boolean validarConexiones() throws IOException, SQLException {
		boolean valido = false;
		Connection conexionValidar = null;

		try {
			conexionValidar = realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));
			if (conexionValidar != null) {
				valido = true;
			}
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorValidarConexion(e.getMessage()));
		} finally {
			if (conexionValidar != null) {
				conexionValidar.close();
			}
		}

		return valido;
	}

	// Metodo para obtener el token de autenticacion para poder consultar
	// los datos de conexion a las distintas bases de datos
	private String obtenerTokenAutorizacionAPIConexiones() throws IOException {
		String token = null;
		try(FileInputStream entradaDatos = new FileInputStream(generalConstantes.RUTA_ARCHIVO_PROPERTIES)) {
			Properties config = new Properties();
			if (Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {
				config.load(entradaDatos);
			}

			HttpClient clienteHTTP = HttpClient.newHttpClient();
			Map<String, Object> cuerpoPeticion = new HashMap<>();
			cuerpoPeticion.put("sitibundus", config.getProperty("token.sitibundus"));
			cuerpoPeticion.put("undivago", config.getProperty("token.undivago"));
			cuerpoPeticion.put("usertype", Integer.valueOf(config.getProperty("token.userType")));

			ObjectMapper objectMapper = new ObjectMapper();
		    String cuerpoJSON = objectMapper.writeValueAsString(cuerpoPeticion);
			HttpResponse<String> peticion = clienteHTTP.send(
				HttpRequest
					.newBuilder(new URI(config.getProperty("base") + ":" + config.getProperty("token.puerto") + config.getProperty("token.url")))
					.header("Accept", "*/*")
					.header("Content-Type", "application/json")
					.POST(BodyPublishers.ofString(cuerpoJSON))
				    .timeout(Duration.ofSeconds(10)) // Tiempo máximo de conexión // Establece un límite de tiempo de 10 segundos
					.build(),
				BodyHandlers.ofString()
			);

			int estatusRespuesta = peticion.statusCode();
			String respuesta = peticion.body();

			if (estatusRespuesta >= generalConstantes.CODIGO_ESTATUS_400) {
				throw new Exception(generalConstantes.mensajeErrorAPICredenciales(String.valueOf(estatusRespuesta), respuesta));
			} else if (estatusRespuesta == generalConstantes.CODIGO_ESTATUS_204) {
				throw new Exception(generalConstantes.MENSAJE_API_TOKEN_NO_EXISTE);
			}

			token = (String)objectMapper.readValue(respuesta, Map.class).get("id_token");
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorConexion(e.getMessage()));
		}

		return token;
	}
}
