package capaDeDatos;

import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Properties;

import capaDeLogicaDeNegocios.LogCLN;
import constantes.generalConstantes;
import utilidades.JasyptEncriptacion;
import utilidades.Utilidades;

public class IngresosCD {

	static LogCLN log = new LogCLN();
	public Connection conexion;
	private JasyptEncriptacion encriptacion = new JasyptEncriptacion();

	// Metodo para crear la conexion a ingresos de postgresql
	public Connection conexionBD() throws IOException, SQLException {
		FileInputStream entradaDatos = null;
		try {
			 	
			Properties propiedades = new Properties();
			entradaDatos = new FileInputStream(generalConstantes.RUTA_ARCHIVO_PROPERTIES);
			if(Utilidades.checkAuthorization(generalConstantes.AUTHORIZATION)) {				
				propiedades.load(entradaDatos);
			}

			String jdbcUrl = propiedades.getProperty("jdbcUrl");
			
			this.conexion = DriverManager.getConnection(
						jdbcUrl, 
						encriptacion.decifrar(propiedades.getProperty("usuario")), 
						encriptacion.decifrar(propiedades.getProperty("clv"))
					);
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorConexion(e.getMessage()));
		} finally {
			if (entradaDatos != null) {
	        	entradaDatos.close();
		    }
		}

		return this.conexion;
	}
	// Metodo para validar que se pueda realizar la conexion a la base de datos
	public boolean validarConexion() throws IOException, SQLException {
		boolean valido = false;

		try {
			this.conexion = conexionBD();
			if (this.conexion != null && !this.conexion.isClosed()) valido = this.conexion.isValid(50000);
		} catch (Exception e) {
			log.grabarLog(generalConstantes.mensajeErrorValidarConexion(e.getMessage()));
		} finally {
			if (this.conexion != null) this.conexion.close();
		}
		
		return valido;
	}

	public ResultSet ejecutarQuery(PreparedStatement stmt) {

		return null;

	}

}
