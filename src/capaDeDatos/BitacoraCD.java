package capaDeDatos;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import capaDeLogicaDeNegocios.LogCLN;
import constantes.generalConstantes;
import entidades.BitacoraInterfase;
import utilidades.Utilidades;

public class BitacoraCD {

	private static ConexionCD conexion = new ConexionCD();
	private static Connection ingresosBD = null;

	static LogCLN log = new LogCLN();

	// Metodo para grabar en la bitacora de base de datos el estatus del proceso ejecucion
	public static void iniciarBitacora(BitacoraInterfase interfaz, int tipoEjecucion) throws IOException, SQLException {

		try {
			ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

			PreparedStatement stmt = ingresosBD.prepareStatement(
					"SELECT fun_grabar_historico_conciliaciones FROM fun_grabar_historico_conciliaciones("
							+ "?::INTEGER, ?::BIT, ?::INTEGER, ?::VARCHAR, ?::VARCHAR);");
			stmt.setInt(1, interfaz.iduProceso);
			stmt.setInt(2, tipoEjecucion);
			stmt.setInt(3, 1);
			stmt.setString(4, "Inicializa el proceso");
			stmt.setString(5, interfaz.iduUsuario);

			ResultSet informacion = stmt.executeQuery();

			if (informacion.next()) {
				interfaz.iduBitacora = informacion.getInt("fun_grabar_historico_conciliaciones");
			}

		} catch (Exception e) {
			log.grabarLog(generalConstantes.MENSAJE_ERROR_INICIAR_BITACORA + e);

		} finally {
			if (!ingresosBD.isClosed()) {
				ingresosBD.close();
			}
		}

	}

	// Metodo para actualizar en la bitacora de base de datos el estatus del proceso ejecucion
	public static void actualizarEstatusBitacora(int estatus, String mensaje, BitacoraInterfase interfaz)
			throws IOException, SQLException {

		try {
			ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

			PreparedStatement stmt;
			stmt = ingresosBD.prepareStatement(
					"SELECT fun_actualizar_historico_conciliaciones FROM fun_actualizar_historico_conciliaciones(?::INTEGER, ?::INTEGER, ?::VARCHAR, ?::VARCHAR)");
			stmt.setInt(1, interfaz.iduBitacora);
			stmt.setInt(2, estatus);
			stmt.setString(3, mensaje);
			stmt.setString(4, interfaz.rutaArchivoSalida);

			stmt.executeQuery();

		} catch (Exception e) {
			log.grabarLog(generalConstantes.MENSAJE_ERROR_ACTUALIZAR_BITACORA  + e);

		} finally {
			if (!ingresosBD.isClosed()) {
				ingresosBD.close();
			}

		}
	}

	// Metodo para grabar o actualizar en base de datos el estatus del paso en ejecucion en el detalle de la bitacora
	public static void actualizarEstatusBitacoraDetalle(int estatus, String mensaje, BitacoraInterfase interfaz)
			throws IOException, SQLException {

		try {
			ingresosBD = conexion.realizarConexionPorId(Utilidades.obtenerPropiedadNumericaPorClave("categorias.ingresos"));

			PreparedStatement stmt;
			stmt = ingresosBD.prepareStatement(
					"SELECT fun_grabar_historico_conciliaciones_detalle FROM fun_grabar_historico_conciliaciones_detalle("
							+ "?::INTEGER, " + "?::INTEGER, " + "?::INTEGER, " + "?::INTEGER, " + "?::VARCHAR, " + "?::VARCHAR);");
			stmt.setInt(1, interfaz.iduBitacora);
			stmt.setInt(2, interfaz.iduEtapa);
			stmt.setInt(3, interfaz.iduPaso);
			stmt.setInt(4, estatus);
			stmt.setString(5, mensaje);
			stmt.setString(6, interfaz.iduUsuario);

			stmt.executeQuery();

		} catch (Exception e) {
			log.grabarLog(generalConstantes.MENSAJE_ERROR_ACTUALIZAR_BITACORA_DETALLE + e);

		} finally {
			if (!ingresosBD.isClosed()) {
				ingresosBD.close();
			}

		}

	}
}